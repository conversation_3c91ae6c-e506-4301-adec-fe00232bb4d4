var _body = "",
    _html = "",
    _htmlBody = "",
    _window = "",
    _layout = "",
    SITE_URL = jQuery("meta[name=sitepath]").attr('content'),
    IS_MOBILE = jQuery("#is_mobile").val() == "Y" ? true : false;

/*PWA Code Start*/
window.addEventListener("load", () => {
	if ("serviceWorker" in navigator) {
		navigator.serviceWorker.register("/dop-sw.js");
	}
});

// Initialize deferredPrompt for use later to show browser install prompt.
if(jQuery(".nf-topbar-b").length > 0) {

    let deferredPrompt;

    window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent the mini-infobar from appearing on mobile
        e.preventDefault();
        
        // Stash the event so it can be triggered later.
        deferredPrompt = e;

        var isMobile = jQuery("#is_mobile").val();
        if(isMobile == "Y" && jQuery('.nf-topbar-b').hasClass('active'))  {
            jQuery('.nf-topbar-b').removeClass('not-show');
            jQuery('.nf-topbar-b').removeClass('active');
        }

        jQuery("#install-app-popup-btn").removeClass('d-none');

        // Optionally, send analytics event that PWA install promo was shown.
        console.log(`'beforeinstallprompt' event was fired.`);
    });

    if(jQuery("#install-app-popup-btn").length > 0) {

        document.getElementById("install-app-popup-btn").addEventListener('click', async (e) => {
            e.preventDefault();

            //hide install app link
            jQuery("#install-app-popup-btn").addClass('d-none');

            // Show the install prompt
            deferredPrompt.prompt();
            // Wait for the user to respond to the prompt
            const {outcome} = await deferredPrompt.userChoice;
            // Optionally, send analytics event with outcome of user choice
            console.log(`User response to the install prompt: ${outcome}`);
            // We've used the prompt, and can't use it again, throw it away
            deferredPrompt = null;
        });
    }

    window.addEventListener('appinstalled', () => {
        //hide install app link
        jQuery("#install-app-popup-btn").addClass('d-none');
        // Clear the deferredPrompt so it can be garbage collected
        deferredPrompt = null;
        // Optionally, send analytics event to indicate successful install
        console.log('PWA was installed');
    });
}
/*PWA Code End*/

jQuery(document).ready(function($) {
    
    initilizeShare();

    //track adobe event
    jQuery(document).on('click', '.cc-adobe-track', function() {

        var href = jQuery(this).attr('href');
        var eventName = jQuery(this).attr('data-en');
        var moduleName = jQuery(this).attr('data-mn');
        var linkText = jQuery(this).attr('data-lt');
        var linkUrl = jQuery(this).attr('data-url');

        var modulePosition = jQuery(this).attr('data-mp') ? jQuery(this).attr('data-mp') : 1;
        var elementType = jQuery(this).attr('data-et') ? jQuery(this).attr('data-et') : 'button';
        var linkText = typeof linkText !== 'undefined' && linkText !== false ? linkText : jQuery(this).text();
        var linkUrl = typeof linkUrl !== 'undefined' && linkUrl !== false ? linkUrl : (href ? href : null);

        adobe_track_event(eventName, 'click', moduleName, modulePosition, elementType, linkText, linkUrl);
        //return false;
    });

    if(jQuery("#twitch-embed").length > 0)  {

        var twitch_id = jQuery("#twitch-embed").attr('data-id');

        TwitchPlayer = new Twitch.Embed("twitch-embed", {
            width: '100%',
            height: '100%',
            channel: twitch_id,
            allowfullscreen : true,
            layout : 'video'
        });
    }
});

function initilizeShare()  {
    
    if(jQuery("#share-details").length < 1)
        return;
    
	//share texts
	var share_url = jQuery('#share-details .share_url').text();
	var twitter_text = jQuery('#share-details .twitter_text').html();
	var mail_subject = jQuery('#share-details .mail_subject').html();
	var mail_body = jQuery('#share-details .mail_body').html().replace(/<br>/g, "");
	var native_share_title = jQuery('#share-details .native_share_title').html();
	var native_share_text = jQuery('#share-details .native_share_text').html();
    
    if (navigator.share && cc_is_mobile()) {

        jQuery('.cc-mobile').removeClass('d-none');

        jQuery(document).on('click', '.btn-share', function(e) {
            e.preventDefault();
            
            navigator.share({
                title: native_share_title,
                text: native_share_text,
                url: share_url
            }).then(() => cc_stats('native_share'))
            .catch((error) => console.log('Error sharing', error));
            
            return false;
        });
    }
    else
    {
        jQuery('.cc-desktop').removeClass('d-none');
        
        //share link
        jQuery(document).on('click', '.btn-share', function(e) {
            
            if(jQuery(this).hasClass('facebook'))  {
                cc_share(share_url, "Facebook", '', '');
				cc_stats('facebook_share');
            }
            else if(jQuery(this).hasClass('twitter'))  {
                cc_share(share_url, "Twitter", twitter_text, '');
				cc_stats('twitter_share');
            }
            else if(jQuery(this).hasClass('email'))  {
                cc_share(share_url, "Email", mail_subject, mail_body);
				cc_stats('mail_share');
            }
            
            return false;
        });
    }    
}

function cc_stats(stat)  {

    cc_ajax('save-stat', { 'stat' : stat });
}

function adobe_track_event(eventName, eventAction, moduleName, modulePosition, elementType, linkText, linkUrl)  {

    if(adobeTrackOn)  {

        if(typeof(digitalData) != "undefined")  {

            eventAction = 'adv_click';

            var eventDetails = {
                eventInfo: {
                    eventName: eventName, //"select faqs", // Name of the event
                    eventAction: eventAction, //"click"
                    timeStamp: Math.round(new Date().getTime()/1000)
                },
                attributes: {
                    assetName: null, // returns the name of an asset. Returns null if not applicable
                    moduleName: moduleName, //"header", // returns the name of the section where the action occurred
                    position: modulePosition,  //"3", // returns a numerical value to identify the position of an item within a module
                    elementType: elementType, //"link", // returns the type of element e.g. "link", "image", "button", "tab"
                    label: jQuery.trim(linkText), //"faqs", // returns the text of the clicked element (if applicable)
                    linkURL: (linkUrl && linkUrl != "#" ? linkUrl : null), //"https://psin.playstation.com/faq", // returns the URL of the link if applicable. Otherwise returns null
                    userActionFlag: "true" // set to "true" by default - this is used by Adobe Launch to know when to fire the relevant rule
                }
            };

            //console.log(eventDetails);

            digitalData.event = new Array();
            digitalData.event[0] = eventDetails;
        }

        if(typeof(_satellite) != "undefined")  {
            _satellite.track('clickEvent');
        }
    }
}

function cc_share(share_url, title, share_text, email_body)  {

	share_text = share_text.replace("[URL]", share_url);
	email_body = email_body.replace("[URL]", share_url);

	if(title == "Twitter"){
		var url = 'https://twitter.com/intent/tweet?text=' + encodeURIComponent(share_text);
	}
	else if(title == "Facebook"){
		var url = 'https://www.facebook.com/sharer.php?u=' + encodeURIComponent(share_url);
	}
	else if(title == "Email"){
        
		if(share_text)  {
			var url = "mailto:?subject="+encodeURIComponent(share_text)+"&body="+encodeURIComponent(email_body);
		}
        else  {
			var url = "mailto:?body="+encodeURIComponent(email_body);
		}
		window.location = url;
		return false;
	}

	window.open(url, title, "status = 1, height = 500, width = 500, resizable = 0");
}

function cc_is_mobile() {
    
    (function(a){(jQuery.browser=jQuery.browser||{}).mobile=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0,4))})(navigator.userAgent||navigator.vendor||window.opera);
    
    return jQuery.browser.mobile;
}

function numberWithDots (number, decimals, dec_point, thousands_sep) {

    // Strip all characters but numerical ones.
    number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
    var n = !isFinite(+number) ? 0 : +number,
        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
        sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
        dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
        s = '',
        toFixedFix = function (n, prec) {
            var k = Math.pow(10, prec);
            return '' + Math.round(n * k) / k;
        };

    // Fix for IE parseFloat(0.55).toFixed(0) = 0;
    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || '').length < prec) {
        s[1] = s[1] || '';
        s[1] += new Array(prec - s[1].length + 1).join('0');
    }
    return s.join(dec);
}