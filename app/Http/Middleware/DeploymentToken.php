<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class DeploymentToken{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next){
        $token = $request->input("token");
        if($token != config("app.deploy_token")) return redirect('https://www.playstation.com');
        return $next($request);
    }
}
