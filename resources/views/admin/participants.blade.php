@extends('admin.layouts.layout')
@section('content')

    @section('custom_css')
    <link href="{{ asset('public/assets') }}/vendors/css/dataTables.bs5.min.css" rel="stylesheet" type="text/css" />
    @endsection

    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">Participants</h5>
                </div>
            </div>
            <div class="page-header-right ms-auto">
                <div class="page-header-right-items">
                    <div class="d-flex d-md-none">
                        <a href="javascript:void(0)" class="page-header-right-close-toggle">
                            <i class="feather-arrow-left me-2"></i>
                            <span>Back</span>
                        </a>
                    </div>
                    <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                        <a href="{{ route("admin_export_participants") }}" class="btn btn-md btn-info">
                            <i class="feather-plus me-2"></i>
                            <span>Export All Participants</span>
                        </a>

                    </div>
                </div>
                <div class="d-md-none d-flex align-items-center">
                    <a href="javascript:void(0)" class="page-header-right-open-toggle">
                        <i class="feather-align-right fs-20"></i>
                    </a>
                </div>
            </div>
        </div>
        <!-- [ page-header ] end -->
        <!-- [ Main Content ] start -->
        
        <div class="main-content">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        
                        <div class="card-body">
                            
                            <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                                @foreach($competitions as $key => $competition)
                                    <li class="nav-item">
                                        <a class="nav-link {{ ($key == 0 ) ? 'active' : '' }}" data-bs-toggle="tab" href="#kt_tab_pane_{{ $key }}">{!! date('d.m.', strtotime($competition->date));   !!}</a>
                                    </li>
                                @endforeach
                            </ul>
    
                            <div class="tab-content" id="myTabContent">
                                
                                @foreach($competitions as $key => $competition)
    
                                    <div class="tab-pane fade {{ ($key == 0 ) ? 'show active' : '' }}" id="kt_tab_pane_{{ $key }}" role="tabpanel">
                                        
                                        <div class="card">
                                            <div class="card-body p-0">
                                                
                                                <div class="table-responsive">
                                                    <table class="table table-hover datatable_list" id="kt_records_table-{{ $key }}" data-ajax-url="{{ route('admin_ajax_participant_list', ['competition_id' => $competition->id ]) }}" data-sort-col="1" data-sort-order="asc" data-record-name="0">
                                                        <thead>
                                                            <tr>
                                                                <th>Nickname</th>
                                                                <th>Email</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            
                                                        </tbody>
                                                    </table>
                                                </div>
                                                
                                            </div>
                                        </div>

                                        
                                    </div>
                                @endforeach
    
                            </div>
                            
                        </div>
                    </div>
                </div>


            </div>

        </div>
        
        <!-- [ Main Content ] end -->
    </div>

    @section('custom_javascript')
        <script src="{{ asset('public/assets') }}/vendors/js/dataTables.min.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/dataTables.bs5.min.js"></script>
    @endsection

@endsection