@extends('admin.layouts.layout')
@section('content')

    @section('custom_css')
        <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/datepicker.min.css">
    @endsection

    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">Math Riddle</h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route("admin_math_riddles") }}">Back</a></li>
                    <li class="breadcrumb-item">{{ @$math_riddle_id ? 'Update' : 'Create' }}</li>
                </ul>
            </div>
            <div class="page-header-right ms-auto">
                <div class="page-header-right-items">
                    <div class="d-flex d-md-none">
                        <a href="javascript:void(0)" class="page-header-right-close-toggle">
                            <i class="feather-arrow-left me-2"></i>
                            <span>Back</span>
                        </a>
                    </div>
                    <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                        <a href="javascript:void(0);" class="btn btn-primary cc-form-submit-btn">
                            <i class="feather-save me-2"></i>
                            <span>Save</span>
                        </a>
                    </div>
                </div>
                <div class="d-md-none d-flex align-items-center">
                    <a href="javascript:void(0)" class="page-header-right-open-toggle">
                        <i class="feather-align-right fs-20"></i>
                    </a>
                </div>
            </div>
        </div>
        <!-- [ page-header ] end -->
        <!-- [ Main Content ] start -->
        <div class="main-content min-vh-100">
            <form id="kt_math_riddle_form" class="form" action="{{ route("admin_math_riddle_save", ["id" => @$math_riddle_id ?: ""]) }}" method="POST">
                <div class="row">
                    <div class="col-xl-12">
                        
                        <div class="card stretch stretch-full">
                            <div class="card-body">
                                
                                @if (session('suc_message'))
                                    <div class="alert alert-success">{{session('suc_message')}}</div>
                                @endif

                                @if (session('err_message'))
                                    <div class="alert alert-danger">{{session('err_message')}}</div>
                                @endif

                                <div class="row">

                                    @include('admin.form.text_component',  ['name' => 'day', 'value' => (old('day') ?: (@$day ?: "")), 'label' => "Math Riddle Day", 'required' => true, 'additionalClass' => 'col-md-2', 'inputCls' => 'datepicker_field'])
    
                                    
                                    @include('admin.form.text_component', ['name' => 'math_riddle_title', 'value' => (old('math_riddle_title') ?: (@$math_riddle_title ?: "")), 'label' => 'Math Riddle Title', 'required' => true, 'additionalClass' => 'col-md-4'])
    
                                    @include('admin.form.text_component',  ['name' => 'answer', 'value' => (old('answer') ?: (@$answer ?: "")), 'label' => "Answer", 'required' => true, 'additionalClass' => 'col-md-2'])
                                    
                                </div>        
                                <hr>
                                <div class="row">
                                
                                    @include('admin.form.ajax_image_upload_component',  ['name' => 'riddle_image', 'value' => (old('riddle_image') ?: (@$riddle_image ?: "")), 'label' => "Desktop Riddle Image (1280 x 720)" , 'required' => true, 'additionalClass' => 'col-md-4'])
    
                                    @include('admin.form.ajax_image_upload_component',  ['name' => 'riddle_image_mobile', 'value' => (old('riddle_image_mobile') ?: (@$riddle_image_mobile ?: "")), 'label' => "Mobile Riddle Image (640 x 360)" , 'required' => false, 'additionalClass' => 'col-md-4'])
    
                                </div> 

                            </div>
                        </div>
                    </div>
                </div>

                @if(@$math_riddle_id)
                    <input type="hidden" name="math_riddle_id" value="{{ $math_riddle_id }}">
                @endif

                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                <button type="submit" class="d-none cc-form-submit">Submit</button>
            </form>    
        </div>
        <!-- [ Main Content ] end -->
    </div>

    @section('custom_javascript')
        <script src="{{ asset('public/assets') }}/js/tinymce/tinymce.bundle.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/datepicker.min.js"></script>
    @endsection

@endsection