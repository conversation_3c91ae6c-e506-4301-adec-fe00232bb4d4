@extends('admin.layouts.layout')
@section('content')

    @section('custom_css')
        <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/datepicker.min.css">
    @endsection

    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">Find The Mistake</h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route("admin_find_the_mistakes") }}">Back</a></li>
                    <li class="breadcrumb-item">{{ @$find_the_mistake_id ? 'Update' : 'Create' }}</li>
                </ul>
            </div>
            <div class="page-header-right ms-auto">
                <div class="page-header-right-items">
                    <div class="d-flex d-md-none">
                        <a href="javascript:void(0)" class="page-header-right-close-toggle">
                            <i class="feather-arrow-left me-2"></i>
                            <span>Back</span>
                        </a>
                    </div>
                    <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                        <a href="javascript:void(0);" class="btn btn-primary cc-form-submit-btn">
                            <i class="feather-save me-2"></i>
                            <span>Save</span>
                        </a>
                    </div>
                </div>
                <div class="d-md-none d-flex align-items-center">
                    <a href="javascript:void(0)" class="page-header-right-open-toggle">
                        <i class="feather-align-right fs-20"></i>
                    </a>
                </div>
            </div>
        </div>
        <!-- [ page-header ] end -->
        <!-- [ Main Content ] start -->
        <div class="main-content min-vh-100">
            <form id="kt_find_the_mistake_form" class="form" action="{{ route("admin_find_the_mistake_save", ["id" => @$find_the_mistake_id ?: ""]) }}" method="POST">
                <div class="row">
                    <div class="col-xl-12">
                        
                        <div class="card stretch stretch-full">
                            <div class="card-body">
                                
                                @if (session('suc_message'))
                                    <div class="alert alert-success">{{session('suc_message')}}</div>
                                @endif

                                @if (session('err_message'))
                                    <div class="alert alert-danger">{{session('err_message')}}</div>
                                @endif

                                <div class="row">
                                    @include('admin.form.text_component',  ['name' => 'day', 'value' => (old('day') ?: (@$day ?: "")), 'label' => "Mistake Day", 'required' => true, 'additionalClass' => 'col-md-2', 'inputCls' => 'datepicker_field'])
                                    
                                    @include('admin.form.text_component', ['name' => 'title', 'value' => (old('title') ?: (@$title ?: "")), 'label' => 'Title', 'required' => true, 'additionalClass' => 'col-md-4'])
                                </div>        
                                
                                <div class="row">

                                    @include('admin.form.ajax_image_upload_component',  ['name' => 'image', 'value' => (old('image') ?: (@$image ?: "")), 'label' => "Image (1280 x 720)" , 'required' => true, 'additionalClass' => 'col-md-4'])
    
                                    @include('admin.form.ajax_image_upload_component',  ['name' => 'mistake_image', 'value' => (old('mistake_image') ?: (@$mistake_image ?: "")), 'label' => "Mistake Image (1280 x 720)" , 'required' => true, 'additionalClass' => 'col-md-2'])

                                    @if(@$find_the_mistake_id)
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-sm btn-primary find_the_mistake_image" data-image="{{ (@$mistake_image) ? url('public/uploads').'/'.$mistake_image : '' }}" data-id="{{ $find_the_mistake_id }}" >Add Spots</button>
                                        </div>
                                        <div class="error_spots_value d-none">{!! @$error_spots !!}</div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                @if(@$find_the_mistake_id)
                    <input type="hidden" name="find_the_mistake_id" value="{{ $find_the_mistake_id }}">
                @endif

                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                <button type="submit" class="d-none cc-form-submit">Submit</button>
            </form>    
        </div>
        <!-- [ Main Content ] end -->
    </div>


    @section('modals')
    <!--begin::Modal - New Target-->
    <div class="modal fade" id="find_the_mistake_image" tabindex="-1" aria-hidden="true">
        <!--begin::Modal dialog-->
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <!--begin::Modal content-->
            <div class="modal-content rounded">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-end">
                    <!--begin::Close-->
                    <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                        <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                        <span class="svg-icon svg-icon-1">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor" />
                                <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor" />
                            </svg>
                        </span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-15">
                    <!--begin:Form-->
                    <form action="{{ route("admin_find_the_mistake_image_form") }}" class="form" method="post" enctype="multipart/form-data" id="find_the_mistake_image_form">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <!--begin::Heading-->
                        <div class="mb-13 text-center">
                            <!--begin::Title-->
                            <h1 class="mb-3">Add Spots</h1>
                            <!--end::Title-->
                        </div>
                        <!--end::Heading-->
                        <!--begin::Input group-->
                        <div class="row mb-5">
                            <div class="col-12">
                                <div id="imageWrapper">
                                    <img src="" class="find_the_mistake_image_src w-100">
                                </div>
                            </div>
                        </div>
                        <!--end::Input group-->
                        <!--begin::Actions-->
                        <div class="text-center">
                            <input type="hidden" name="error_spots" value="">
                            <input type="hidden" name="mistake_id">
                            <button type="submit" class="btn btn-primary ms-auto">Done</button>
                        </div>
                        <!--end::Actions-->
                    </form>
                    <!--end:Form-->
                </div>
                <!--end::Modal body-->
            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - New Target-->
    @endsection
    
    @section('custom_javascript')
        <script src="{{ asset('public/assets') }}/js/tinymce/tinymce.bundle.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/datepicker.min.js"></script>
    @endsection

@endsection