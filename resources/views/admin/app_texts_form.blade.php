@extends('admin.layouts.layout')

@section('content')

    @section('custom_css')
        <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/select2.min.css">
        <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/select2-theme.min.css">
        <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/datepicker.min.css">
    @endsection

@php
    
    //define field types here if there is special case
    $text_fields = [];
    $textarea_fields = ['a_layout.a_meta.b_description','d_pages.f_participate_form.k_accept_text'];
    $editor_fields = [];
    $image_fields = [];
    $multiple_image_fields = ['a_layout.c_manifest_json.h_desktop_screenshot_1024x593_image','a_layout.c_manifest_json.h_mobile_screenshot_540x720_image'];
    $toggle_fields = [];
    $audio_fields = [];
    $pdf_fields = [];

@endphp
    
    <div class="nxl-content without-header nxl-full-content">
        
        @if(!empty($tabs) || $active_menu == "app_texts")

            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">Text Management</h5>
                    </div>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex d-md-none">
                            <a href="javascript:void(0)" class="page-header-right-close-toggle">
                                <i class="feather-arrow-left me-2"></i>
                                <span>Back</span>
                            </a>
                        </div>
                        
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            
                            <a href="{{ route('admin_export_texts', ['module' => $module]) }}" class="btn btn-info">
                                <i class="feather-download me-2"></i>
                                <span>Export Text</span>
                            </a>

                            <a href="javascript:;" data-bs-toggle="modal" data-bs-target="#import_modal" class="btn btn-primary">
                                <i class="feather-upload me-2"></i>
                                <span>Import Text</span>
                            </a>
                            
                        </div>

                    </div>
                    <div class="d-md-none d-flex align-items-center">
                        <a href="javascript:void(0)" class="page-header-right-open-toggle">
                            <i class="feather-align-right fs-20"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- [ Main Content ] start -->
            <div class="main-content d-flex">
                
                <!-- [ Content Sidebar ] start -->
                <div class="content-sidebar content-sidebar-md" data-scrollbar-target="#psScrollbarInit">
                    <div class="content-sidebar-body">

                        <ul class="nav flex-column nxl-content-sidebar-item">
                            
                            @foreach($tabs as $tabKey => $tabLabel)
                                @if($tabLabel != 'mail')
                                    <li class="nav-item flex-fill">
                                        <a href="javascript:void(0);" class="nav-link {{ (($tabKey < 1 || ($tabs[0] == "mail" && $tabKey < 2)) ? 'active' : '') }}" data-bs-toggle="tab" data-bs-target="#texts-{{ $tabKey }}-tab">{{ textLabel($tabLabel) }}</a>
                                    </li>
                                @endif    
                            @endforeach

                        </ul>
                    </div>
                </div>
                <!-- [ Content Sidebar  ] end -->
                <!-- [ Main Area  ] start -->
                <div class="content-area" data-scrollbar-target="#psScrollbarInit">
                    <div class="content-area-body">
                        <div class="card mb-0">
                            <div class="card-body">
                                <div class="tab-content">
                                    
                                    <form method="post" class="has-validation-callback" id="app_text_form">
                                        
                                        <div class="tab-content" id="myTabContent">

                                            @foreach($tabs as $tabKey => $tabLabel)
                                                @if($tabLabel != 'mail')
                                                    <div class="tab-pane fade {{ (($tabKey < 1 || ($tabs[0] == "mail" && $tabKey < 2)) ? 'show active' : '') }}" id="texts-{{ $tabKey }}-tab" role="tabpanel">
                                                        
                                                        @foreach($text_arr[$tabLabel] as $sectionLabel => $sectionFields)
                                                            
                                                            <div class="card card-bordered mb-6">
                                                                
                                                                <div class="card-header border-top">
                                                                    <h5 class="mb-0">{{ textLabel($sectionLabel) }} Section</h5>

                                                                    <a href="javascript:;" class="avatar-text border-0 bg-soft-danger text-danger ms-auto delete-section-text"  data-bs-toggle="tooltip" data-bs-original-title="Delete" data-key="{{ $tabLabel.".".$sectionLabel."." }}"><i class="feather-trash-2"></i></a>

                                                                </div>

                                                                <div class="card-body">
                                                                    @foreach($sectionFields as $fieldLabel => $fieldVal)

                                                                        @php
                                                                        $fieldName = $tabLabel.".".$sectionLabel.".".$fieldLabel;
                                                                        $field_label = textLabel($fieldLabel)." <small>(".$fieldName.")</small>";

                                                                        $field_type = "text";

                                                                        if(strlen($fieldVal) > 120 || strpos($fieldVal, "\n") !== false)  {
                                                                            $field_type = "textarea";
                                                                        }
                                                                        
                                                                        if(strpos($fieldName, '_image') !== false)  {
                                                                            $field_type = "image";
                                                                        }

                                                                        if(strpos($fieldName, '_pdf') !== false)  {
                                                                            $field_type = "pdf";
                                                                        }

                                                                        if(strpos($fieldName, '_video') !== false)  {
                                                                            $field_type = "mp4";
                                                                        }

                                                                        if(strpos($fieldName, '_webm') !== false)  {
                                                                            $field_type = "webm";
                                                                        }
                                                                        
                                                                        if(strlen($fieldVal) > 160 && isHTMLText($fieldVal))  {
                                                                            $field_type = "editor";
                                                                        }

                                                                        if(in_array($fieldName, $text_fields)) {
                                                                            $field_type = "text";
                                                                        }
                                                                        else if(in_array($fieldName, $textarea_fields)) {
                                                                            $field_type = "textarea";
                                                                        }
                                                                        else if(in_array($fieldName, $editor_fields)) {
                                                                            $field_type = "editor";
                                                                        }
                                                                        else if(in_array($fieldName, $image_fields)) {
                                                                            $field_type = "image";
                                                                        }
                                                                        else if(in_array($fieldName, $multiple_image_fields)) {
                                                                            $field_type = "multiple_image";
                                                                        }
                                                                        else if(in_array($fieldName, $toggle_fields)) {
                                                                            $field_type = "toggle";
                                                                        }
                                                                        else if(in_array($fieldName, $audio_fields)) {
                                                                            $field_type = "audio";
                                                                        }
                                                                        else if(in_array($fieldName, $pdf_fields)) {
                                                                            $field_type = "pdf";
                                                                        }
                                                                        @endphp

                                                                        @if($field_type == "textarea" || $field_type == "editor")

                                                                            @if($is_many_languages)

                                                                                <div class="row">
                                                                                    @foreach(getLanguageLocaleIds() as $loopLangCode => $loopLangId)
                                                                            
                                                                                        @include('admin.form.textarea_component', ['name' => $fieldName, 'value' => @$allTexts[$loopLangCode][$fieldName], 'id' => str_replace('.','_',$fieldName).'_'.$loopLangCode, 'label' => langLabel($field_label, $loopLangCode), 'additionalClass' => 'col-md-6', 'is_language' => true, 'inputCls' => 'language-editor '.str_replace('.','_',$fieldName).$loopLangCode, 'language_prefix' => str_replace('.','_',$fieldName), 'lang_code' => $loopLangCode, 'editor' => ($field_type == "editor" ? true : false)])

                                                                                    @endforeach

                                                                                </div>
                                                                            @else        
                                                                                @include('admin.form.textarea_component', ['name' => $fieldName, 'value' => $fieldVal, 'id' => str_replace(".", "_", $fieldName), 'label' => $field_label, 'editor' => ($field_type == "editor" ? true : false)])
                                                                            @endif    

                                                                        @elseif($field_type == "image")

                                                                            @if($is_many_languages)

                                                                                <div class="row">
                                                                                    @foreach(getLanguageLocaleIds() as $loopLangCode => $loopLangId)
                                                                            
                                                                                        @include('admin.form.ajax_image_upload_component', ['name' => $fieldName, 'value' => @$allTexts[$loopLangCode][$fieldName], 'id' => str_replace('.','_',$fieldName).'_'.$loopLangCode, 'label' => langLabel($field_label, $loopLangCode), 'additionalClass' => 'col-md-6 text-image-upload', 'is_language' => true, 'inputCls' => str_replace('.','_',$fieldName).$loopLangCode, 'language_prefix' => str_replace('.','_',$fieldName), 'lang_code' => $loopLangCode ])

                                                                                    @endforeach

                                                                                </div>
                                                                            @else 
                                                                                @include('admin.form.ajax_image_upload_component', ['name' => $fieldName, 'value' => $fieldVal, 'id' => str_replace(".", "_", $fieldName), 'label' => $field_label, 'additionalClass' => 'text-image-upload'])
                                                                            @endif    

                                                                        @elseif($field_type == "multiple_image")

                                                                            @if($is_many_languages)

                                                                                <div class="row">
                                                                                    @foreach(getLanguageLocaleIds() as $loopLangCode => $loopLangId)
                                                                            
                                                                                        @php
                                                                                        $fieldVal = @$allTexts[$loopLangCode][$fieldName];
                                                                                        $fieldValues = $fieldVal ? explode(",", $fieldVal) : [];
                                                                                        @endphp
                                                                                        
                                                                                        @include('admin.form.ajax_multiple_image_upload_component', ['name' => $fieldName, 'value' => $fieldValues, 'id' => str_replace('.','_',$fieldName).'_'.$loopLangCode, 'label' => langLabel($field_label, $loopLangCode), 'additionalClass' => 'col-md-6 text-image-upload', 'is_language' => true, 'inputCls' => str_replace('.','_',$fieldName).$loopLangCode, 'language_prefix' => str_replace('.','_',$fieldName), 'lang_code' => $loopLangCode ])

                                                                                    @endforeach

                                                                                </div>
                                                                            @else 

                                                                                @php  $fieldValues = $fieldVal ? explode(",", $fieldVal) : [];  @endphp
                                                                                
                                                                                @include('admin.form.ajax_multiple_image_upload_component', ['name' => $fieldName, 'value' => $fieldValues, 'id' => str_replace(".", "_", $fieldName), 'label' => $field_label, 'additionalClass' => 'text-image-upload'])
                                                                            @endif 

                                                                        @elseif($field_type == "audio")

                                                                            @if($is_many_languages)

                                                                                <div class="row">
                                                                                    @foreach(getLanguageLocaleIds() as $loopLangCode => $loopLangId)
                                                                            
                                                                                        @include('admin.form.ajax_audio_upload_component', ['name' => $fieldName, 'value' => @$allTexts[$loopLangCode][$fieldName], 'id' => str_replace('.','_',$fieldName).'_'.$loopLangCode, 'label' => langLabel($field_label, $loopLangCode), 'additionalClass' => 'col-md-6 text-image-upload', 'is_language' => true, 'inputCls' => str_replace('.','_',$fieldName).$loopLangCode, 'language_prefix' => str_replace('.','_',$fieldName), 'lang_code' => $loopLangCode ])

                                                                                    @endforeach
                                                                                    
                                                                                </div>

                                                                            @else
                                                                            
                                                                                @include('admin.form.ajax_audio_upload_component', ['name' => $fieldName, 'value' => $fieldVal, 'id' => str_replace(".", "_", $fieldName), 'label' => $field_label, 'additionalClass' => 'text-image-upload'])
                                                                            
                                                                            @endif
                                                                                
                                                                        @elseif($field_type == "pdf")
                                                                            
                                                                            @if($is_many_languages)

                                                                                <div class="row">
                                                                                    @foreach(getLanguageLocaleIds() as $loopLangCode => $loopLangId)

                                                                                        @include('admin.form.ajax_pdf_upload_component', ['name' => $fieldName, 'value' => @$allTexts[$loopLangCode][$fieldName], 'id' => str_replace('.','_',$fieldName).'_'.$loopLangCode, 'label' => langLabel($field_label, $loopLangCode), 'additionalClass' => 'col-md-6 text-image-upload', 'is_language' => true, 'inputCls' => str_replace('.','_',$fieldName).$loopLangCode, 'language_prefix' => str_replace('.','_',$fieldName), 'lang_code' => $loopLangCode ])
                                                                                    
                                                                                    @endforeach
                                                                                </div>
                                                                                
                                                                            @else
                                                                                
                                                                                @include('admin.form.ajax_pdf_upload_component', ['name' => $fieldName, 'value' => $fieldVal, 'id' => str_replace(".", "_", $fieldName), 'label' => $field_label, 'additionalClass' => 'text-image-upload'])
                                                                            
                                                                            @endif
                                                                            
                                                                        @elseif($field_type == "toggle")
        
                                                                            @include('admin.form.checkbox_active_component', ['name' => $fieldName, 'checked' => $fieldVal, 'id' => str_replace(".", "_", $fieldName), 'label' => $field_label])
                    
                                                                        @elseif($field_type == "mp4" || $field_type == "webm")
                                                                                
                                                                            @if($is_many_languages)

                                                                                <div class="row">
                                                                                    
                                                                                    @foreach(getLanguageLocaleIds() as $loopLangCode => $loopLangId)    

                                                                                        @include('admin.form.ajax_video_upload_component', ['name' => $fieldName, 'value' => @$allTexts[$loopLangCode][$fieldName], 'id' => str_replace('.','_',$fieldName).'_'.$loopLangCode, 'label' => langLabel($field_label, $loopLangCode), 'additionalClass' => 'col-md-6 text-image-upload', 'is_language' => true, 'inputCls' => str_replace('.','_',$fieldName).$loopLangCode, 'language_prefix' => str_replace('.','_',$fieldName), 'lang_code' => $loopLangCode, 'onlyExt' => ($field_type == "webm") ? 'webm' : 'mp4'  ])

                                                                                    @endforeach
                                                                                    
                                                                                </div>
                                                                                
                                                                            @else
                                                                            
                                                                                @include('admin.form.ajax_video_upload_component', ['name' => $fieldName, 'value' => $fieldVal, 'id' => str_replace(".", "_", $fieldName), 'label' => $field_label, 'additionalClass' => 'text-image-upload', 'onlyExt' => ($field_type == "webm") ? 'webm' : 'mp4'])
                                                                            
                                                                            @endif

                                                                        @else

                                                                            @if($is_many_languages)

                                                                                <div class="row">
                                                                                    @foreach(getLanguageLocaleIds() as $loopLangCode => $loopLangId)
                                                                                    
                                                                                        @include('admin.form.text_component', ['name' => $fieldName, 'id' => str_replace('.','_',$fieldName).'_'.$loopLangCode, 'value' => @$allTexts[$loopLangCode][$fieldName], 'label' => langLabel($field_label, $loopLangCode), 'additionalClass' => 'col-md-6', 'is_language' => true, 'inputCls' => str_replace('.','_',$fieldName).$loopLangCode, 'language_prefix' => str_replace('.','_',$fieldName), 'lang_code' => $loopLangCode])

                                                                                    @endforeach
                                                                                </div>        

                                                                            @else

                                                                                @include('admin.form.text_component', ['name' => $fieldName, 'value' => $fieldVal, 'id' => str_replace(".", "_", $fieldName) , 'label' => $field_label])

                                                                            @endif    
                                                                        @endif
                                                                        <hr>
                                                                    @endforeach
                                                                    
                                                                </div>
                                                            </div>

                                                        @endforeach

                                                    </div>
                                                @endif
                                            @endforeach

                                        </div>
                                        <input type="hidden" name="save_in" value="upload">
                                        <input type="hidden" name="form_name" value="text_form">
                                    </form>
                                </div>

                            </div>
                        </div>
                    </div>
                    
                </div>
                <!-- [ Content Area ] end -->
                
            </div>
            <!-- [ Main Content ] end -->

        @endif
            
    </div>

    @section('modals')
    <!--begin::Modal - New Target-->
    <div class="modal fade" id="import_modal" tabindex="-1" aria-hidden="true" data-suc-msg="Texts Imported Successfully.">
        <!--begin::Modal dialog-->
        <div class="modal-dialog modal-dialog-centered mw-650px">
            <!--begin::Modal content-->
            <div class="modal-content rounded">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-end">
                    <!--begin::Close-->
                    <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                        <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                        <span class="svg-icon svg-icon-1">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor" />
                                <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor" />
                            </svg>
                        </span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-15">
                    <!--begin:Form-->
                    <form action="{{ route("admin_import_texts") }}" class="form" method="post" enctype="multipart/form-data" id="import-form">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <!--begin::Heading-->
                        <div class="mb-13 text-center">
                            <!--begin::Title-->
                            <h1 class="mb-3">Import Texts</h1>
                            <!--end::Title-->
                        </div>
                        <!--end::Heading-->
                        <!--begin::Input group-->
                        <div class="row mb-5">
                            <div class="col-xl-2" ></div>
                            <div class="fv-row mb-7 fv-plugins-icon-container col-xl-8">
                                <label class="required fs-6 fw-semibold mb-2">Choose XLSX File</label>
                                <input type="file" class="form-control form-control-solid" name="texts_file" id="texts_file" required accept="application/msexcel"/>
                            </div>
                            <div class="col-xl-2" ></div>
                        </div>
                        <!--end::Input group-->
                        <!--begin::Actions-->
                        <div class="text-center">
                            <button type="button" id="import-sbmt-btn" class="btn btn-primary ms-auto" data-type="Texts">Submit</button>
                        </div>
                        <!--end::Actions-->
                    </form>
                    <!--end:Form-->
                </div>
                <!--end::Modal body-->
            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - New Target-->
    @endsection

    @section('custom_javascript')
    <script src="{{ asset('public/assets') }}/js/tinymce/tinymce.bundle.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/select2.min.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/select2-active.min.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/datepicker.min.js"></script>

        @if ($errors->any())
            @php
            $err_msgs = [];
            foreach ($errors->all() as $error) {
                $err_msgs[] = "- ".$error;
            }
            $form_error_msg = implode("<br>", $err_msgs);
            @endphp

            <script>
            jQuery(document).ready(function() {
                toast('{!! $form_error_msg !!}', 'danger');
            });
            </script>
        @endif

    @endsection

@endsection