<?php
namespace App\Code;

class SimpleImage{
    var $image;
    var $image_type;
    var $filename;

    function load($filename){
        $image_info     = getimagesize($filename);
        $this->filename = $filename;

        $this->image_type = $image_info[2];
        if($this->image_type == IMAGETYPE_JPEG){
            $this->image = imagecreatefromjpeg($filename);
        }
        elseif($this->image_type == IMAGETYPE_GIF){
            $this->image = imagecreatefromgif($filename);
        }
        elseif($this->image_type == IMAGETYPE_PNG){
            $this->image = imagecreatefrompng($filename);
        }
    }

    function rotateCCW(){
        $degrees     = 90;
        $this->image = imagerotate($this->image, $degrees, 0);
    }

    function rotateCW(){
        $degrees     = 270;
        $this->image = imagerotate($this->image, $degrees, 0);
    }

    function save($filename, $image_type = IMAGETYPE_JPEG, $compression = 75, $permissions = null){
        if($image_type == IMAGETYPE_JPEG){
            imagejpeg($this->image, $filename, $compression);
        }
        elseif($image_type == IMAGETYPE_GIF){

            imagegif($this->image, $filename);
        }
        elseif($image_type == IMAGETYPE_PNG){

            imagepng($this->image, $filename);
        }
        if($permissions != null){

            chmod($filename, $permissions);
        }
    }

    function image_fix_orientation(){
        $exif = exif_read_data($this->filename);

        if(!empty($exif['Orientation'])){
            switch($exif['Orientation']){
                case 3:
                    $this->image = imagerotate($this->image, 180, 0);
                    break;

                case 6:
                    $this->image = imagerotate($this->image, -90, 0);
                    break;

                case 8:
                    $this->image = imagerotate($this->image, 90, 0);
                    break;
            }
        }
    }

    function output($image_type = IMAGETYPE_JPEG){

        if($image_type == IMAGETYPE_JPEG){
            imagejpeg($this->image);
        }
        elseif($image_type == IMAGETYPE_GIF){

            imagegif($this->image);
        }
        elseif($image_type == IMAGETYPE_PNG){

            imagepng($this->image);
        }
    }

    function getWidth(){

        return imagesx($this->image);
    }

    function getHeight(){

        return imagesy($this->image);
    }

    function resizeToHeight($height){

        $ratio = $height / $this->getHeight();
        $width = $this->getWidth() * $ratio;
        $this->resize($width, $height);
    }

    function resizeToWidth($width){
        $ratio  = $width / $this->getWidth();
        $height = $this->getheight() * $ratio;
        $this->resize($width, $height);
    }

    function scale($scale){
        $width  = $this->getWidth() * $scale;
        $height = $this->getheight() * $scale;
        $this->resize($width, $height);
    }

    function resize($width, $height){
        $new_image = imagecreatetruecolor($width, $height);

        if($this->image_type == IMAGETYPE_GIF || $this->image_type == IMAGETYPE_PNG){
            imagealphablending($new_image, false);
            imagesavealpha($new_image, true);
        }

        imagecopyresampled($new_image, $this->image, 0, 0, 0, 0, $width, $height, $this->getWidth(), $this->getHeight());
        $this->image = $new_image;
    }

    function cropImage($nw, $nh, $auto_crop = 0, $x_crop_point = 0, $y_crop_point = 0){
        $w = $this->getWidth();
        $h = $this->getHeight();

        $simg = $this->image;
        $dimg = imagecreatetruecolor($nw, $nh);

        if($this->image_type == IMAGETYPE_GIF || $this->image_type == IMAGETYPE_PNG){
            imagealphablending($dimg, false);
            imagesavealpha($dimg, true);
        }

        if($auto_crop){
            $wm       = $w / $nw;
            $hm       = $h / $nh;
            $h_height = $nh / 2;
            $w_height = $nw / 2;

            if($wm > $hm){
                $adjusted_width = $w / $hm;
                $half_width     = $adjusted_width / 2;
                $int_width      = $half_width - $w_height;
                imagecopyresampled($dimg, $simg, -$int_width, 0, 0, 0, $adjusted_width, $nh, $w, $h);
            }
            elseif(($wm < $hm) || ($wm == $hm)){
                $adjusted_height = $h / $wm;
                $half_height     = $adjusted_height / 2;
                $int_height      = $half_height - $h_height;
                imagecopyresampled($dimg, $simg, 0, -$int_height, 0, 0, $nw, $adjusted_height, $w, $h);
            }
            else{
                imagecopyresampled($dimg, $simg, 0, 0, 0, 0, $nw, $nh, $w, $h);
            }
        }
        else{
            imagecopyresampled($dimg, $simg, 0, 0, $x_crop_point, $y_crop_point, $nw, $nh, $nw, $nh);
        }

        $this->image = $dimg;
    }

    function watermarkImage($watermark_image, $center = 1, $x_pos = 0, $y_pos = 0){
        $im = $this->image;

        $mainWidth       = imagesx($im);
        $mainHeight      = imagesy($im);
        $watermarkWidth  = imagesx($watermark_image);
        $watermarkHeight = imagesy($watermark_image);

        // width to calculate positioning of the stamp.
        if($center == 1){
            $x_pos = ($mainWidth / 2) - ($watermarkWidth / 2);
            $y_pos = ($mainHeight / 2) - ($watermarkHeight / 2);
        }

        imagecopy($im, $watermark_image, $x_pos, $y_pos, 0, 0, $watermarkWidth, $watermarkHeight);

        $this->image = $im;
    }

    //font_size in points and font_color in hex #0f0f0f format
    function write_text($text_to_write, $font_file, $font_size, $font_color, $x_pos = 0, $y_pos = 0){
        $image = $this->image;

        $font_rgb     = hex_to_rgb($font_color);
        $font_color   = ImageColorAllocate($image, $font_rgb['red'], $font_rgb['green'], $font_rgb['blue']);
        $image_width  = imagesx($image);
        $image_height = imagesy($image);

        //text width and height
        $box        = @ImageTTFBBox($font_size, 0, $font_file, $text_to_write);
        $txt_width  = abs($box[2] - $box[0]);
        $txt_height = abs($box[5] - $box[3]);

        //get x position
        if($x_pos)
            $put_text_x = $x_pos;
        else
            $put_text_x = ($image_width - $txt_width) / 2;

        //get y position
        if($y_pos)
            $put_text_y = $y_pos;
        else
            $put_text_y = ($image_height - $txt_height) / 2 + 125;

        //write text
        imagettftext($image, $font_size, 0, $put_text_x, $put_text_y, $font_color, $font_file, $text_to_write);
        $this->image = $image;
    }
}

function hex_to_rgb($hex){
    // remove '#'
    if(substr($hex, 0, 1) == '#')
        $hex = substr($hex, 1);

    // expand short form ('fff') color to long form ('ffffff')
    if(strlen($hex) == 3){
        $hex = substr($hex, 0, 1) . substr($hex, 0, 1) .
            substr($hex, 1, 1) . substr($hex, 1, 1) .
            substr($hex, 2, 1) . substr($hex, 2, 1);
    }

    // convert from hexidecimal number systems
    $rgb['red']   = hexdec(substr($hex, 0, 2));
    $rgb['green'] = hexdec(substr($hex, 2, 2));
    $rgb['blue']  = hexdec(substr($hex, 4, 2));
    return $rgb;
}
