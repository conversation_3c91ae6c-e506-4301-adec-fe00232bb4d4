<?php

namespace App\Http\Models;

use Eloquent;
use Exception;
use App\Framework\src\Code\EzCache;
use App\Framework\src\Http\Traits\ManageableModelTrait;
use Illuminate\Contracts\Cache\Repository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Framework\src\Http\Models\DataRevision;

/**
 * @property integer id
 * @property string day
 * @property string code
 * @property string math_riddle_title
 * @property string answer
 
 * @property string created_at
 * @property string updated_at
 *
 * @mixin Builder
 * @mixin Eloquent
 */
class MathRiddle extends Model{
	use ManageableModelTrait;
	
    protected $table   = 'math_riddles';
    protected $guarded = [];
	
    protected static $NON_ID_FIELDS = [
		
        "day"               => ["type" => "date", "null" => true, 'index' => true],
		"code"              => ["type" => "string", "length" => 50, "null" => true, 'index' => true],
		"math_riddle_title" => ["type" => "text", "null" => true],
		"riddle_image"       => ["type" => "string", "null" => true],
        "riddle_image_mobile"=> ["type" => "string", "null" => true],
        "answer"             => ["type" => "string", "length" => 50, "null" => true, 'index' => true],
        
        // ---
        "created_at" => ["type" => "created_at", "null" => true],
        "updated_at" => ["type" => "updated_at"],
    ];


    /**
     * @return Repository|mixed
     * @throws Exception
     */
    public static function getAll(){
		
		$cacheKey = "math_riddle_" . DataRevision::getRevision();
		
        return  EzCache::get($cacheKey, static function() {
            
            $math_riddles = MathRiddle::orderBy('day', 'ASC')->get();
            return $math_riddles;
        });
    }
}