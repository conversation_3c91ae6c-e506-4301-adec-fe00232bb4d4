<?php

namespace App\Http\Controllers\Site;

use Mobile_Detect;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PWAController extends Controller{

    public $is_mobile;
    
    /**
     * Create a new controller instance.
     *
     * @param Mobile_Detect $mobileDetect
     */
    public function __construct(Mobile_Detect $mobileDetect){

        $this->is_mobile = $mobileDetect->isMobile() && !$mobileDetect->isTablet() ? true : false;
    }

    
    /**
     * @param Request       $request
     * @param Mobile_Detect $mobileDetect
     *
     * @return Application|Factory|View
     * @throws Exception
     */
    public function offlinePage(Request $request){
		
		$viewData = [];
		
		$viewData['page_type'] = "offline";
		$viewData['page_menu'] = "offline";
		$viewData['is_mobile'] = $this->is_mobile;
        
		return view('site.offline', $viewData);
    }


    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function manifestJson(Request $request){

        $start_url = '/days-of-play-2025';

        if(is_localhost())  {

            $site_url = route('site_home');
            $url_parts = explode($_SERVER['HTTP_HOST'], $site_url);
            $start_url = @$url_parts[1];
        }

        if($request->get('lang'))  {
            setUserLanguage(sanitizeStr($request->get('lang')));
        }
        
        //https://dev.to/progressier/why-a-pwa-app-icon-shouldnt-have-a-purpose-set-to-any-maskable-4c78#:~:text=Declaring%20an%20icon%20with%20purpose,much%20or%20too%20little%20padding

        if($this->is_mobile)  {

            //mobile version
            $icons = [
                [
                    "src" => textImagePath('a_layout.c_manifest_json.f_mobile_icon_any_image', 'android-chrome-512x512.png'),
                    "sizes" => "512x512",
                    "type" => "image/png",
                    "purpose" => "any"
                ],
                [
                    "src" => textImagePath('a_layout.c_manifest_json.g_mobile_icon_maskable_image', 'android-chrome-512x512.png'),
                    "sizes" => "512x512",
                    "type" => "image/png",
                    "purpose" => "maskable"
                ]
            ];
        }
        else  {

            //desktop version
            $icons = [
                [
                    "src" => textImagePath('a_layout.c_manifest_json.e_desktop_icon_any_image', 'android-chrome-512x512.png'),
                    "sizes" => "512x512",
                    "type" => "image/png",
                    "purpose" => "any"
                ]
            ];
        }

        $desktop_screenshot_image = fT('a_layout.c_manifest_json.h_desktop_screenshot_1024x593_image');
        $mobile_screenshot_image = fT('a_layout.c_manifest_json.h_mobile_screenshot_540x720_image');

        $screenshots = [];

        $desktop_screenshot_images = $desktop_screenshot_image ? explode(",", $desktop_screenshot_image) : [];
        foreach($desktop_screenshot_images as $desktop_screenshot_image)
        {
            if($desktop_screenshot_image)  {

                $desktop_img = getimagesize(public_path('uploads/'.$desktop_screenshot_image));

                $screenshots[] = [
                    "src" => url('public/uploads/'.$desktop_screenshot_image),
                    "type" => $desktop_img['mime'],
                    "sizes" => $desktop_img[0]."x".$desktop_img[1],
                    "form_factor" => "wide"
                ];
            }
        }    
        
        $mobile_screenshot_images = $mobile_screenshot_image ? explode(",", $mobile_screenshot_image) : [];
        foreach($mobile_screenshot_images as $mobile_screenshot_image)
        {
            if($mobile_screenshot_image)  {

                $mobile_img = getimagesize(public_path('uploads/'.$mobile_screenshot_image));

                $screenshots[] = [
                    "src" => url('public/uploads/'.$mobile_screenshot_image),
                    "type" => $mobile_img['mime'],
                    "sizes" => $mobile_img[0]."x".$mobile_img[1],
                    "form_factor" => "narrow"
                ];
            }
        }    

        $jsonArr = [
            "name" => fT("a_layout.c_manifest_json.a_name", "Project Name"),
            "short_name" => fT("a_layout.c_manifest_json.b_short_name", "Project Short Name"),
            "icons" => $icons,
            
            "start_url" => "./",
            "scope" => ".",
            "theme_color" => fT("a_layout.c_manifest_json.c_theme_color", "#242424"),
            "background_color" => fT("a_layout.c_manifest_json.d_background_color", "#ffffff"),
            "display" => "standalone"
        ];

        if(!empty($screenshots))
            $jsonArr['screenshots'] = $screenshots;

        return response()->json($jsonArr);
    }
}