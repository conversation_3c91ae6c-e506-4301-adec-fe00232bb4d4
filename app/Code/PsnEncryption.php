<?php
namespace App\Code;

class PsnEncryption{
	/**
	 * @return string
	 */
	public static function getEncryptionMethod(): string{
		return "AES-256-CBC";
	}

	/**
	 * @return false|string
	 */
	public static function getEncryptionKey(){
		return base64_decode("htZFcRqe79MmSRmhdevcluGOCToVu6s3rZDAtmY+JLA=");
	}

	/**
	 * @return false|string
	 */
	public static function getIv(){
		return base64_decode("b44BqWoOMlE1iQxUXE+wnA==");
	}

	/**
	 * @param string $text
	 *
	 * @return string
	 * @noinspection EncryptionInitializationVectorRandomnessInspection
	 */
	public static function encryptText(string $text = ""): string{
		return @static::packBase64(@openssl_encrypt($text, static::getEncryptionMethod(), static::getEncryptionKey(), OPENSSL_RAW_DATA, static::getIv()));
	}

	/**
	 * @param $text
	 *
	 * @return false|string
	 * @noinspection EncryptionInitializationVectorRandomnessInspection
	 */
	public static function decryptText($text){
		return openssl_decrypt(static::unpackBase64($text), static::getEncryptionMethod(), static::getEncryptionKey(), OPENSSL_RAW_DATA, static::getIv());
	}

	/**
	 * @param $data
	 *
	 * @return string
	 */
	public static function packBase64($data): string{
		return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
	}

	/**
	 * @param $data
	 *
	 * @return false|string
	 */
	public static function unpackBase64($data){
		return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '='));
	}

	/**
	 * @param int $length
	 *
	 * @return false|string
	 * @throws Exception
	 */
	public static function uniqIdREal(int $length = 13){
		$rndStrFunc = static function ($length = 13){
			return substr(str_shuffle(str_repeat($x = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length / strlen($x)))), 1, $length);
		};
		if(function_exists("random_bytes")){
			$bytes = random_bytes(ceil($length / 2));
		}
		elseif(function_exists("openssl_random_pseudo_bytes")){
			/** @noinspection CryptographicallySecureRandomnessInspection */
			$bytes = openssl_random_pseudo_bytes(ceil($length / 2), $strongResult);
			$bytes = $bytes !== false && $strongResult !== false ? $bytes : $rndStrFunc($length);
		}
		else{
			throw new RuntimeException("No cryptographically secure random function available");
		}
		return substr(bin2hex($bytes), 0, $length);
	}

	/**
	 * @return string
	 * @noinspection CryptographicallySecureRandomnessInspection
	 */
	public static function genIv(): string{
		$rndStrFunc = static function ($length = 10){
			return substr(str_shuffle(str_repeat($x = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length / strlen($x)))), 1, $length);
		};
		$iv         = openssl_random_pseudo_bytes(openssl_cipher_iv_length(static::getEncryptionMethod()), $strongResult);
		$iv         = $strongResult !== false && $iv !== false ? $iv : $rndStrFunc(openssl_cipher_iv_length(static::getEncryptionMethod()));
		return base64_encode($iv);
	}
}
