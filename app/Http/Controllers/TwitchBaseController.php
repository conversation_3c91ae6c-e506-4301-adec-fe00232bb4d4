<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Http\Request;
use Firebase\JWT\JWT;
use Exception;
use stdClass;

class TwitchBaseController extends BaseController{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public $clientId;
    public $secretKey;
    public $extOwnerID;
    public $apiClientSecret;
    public $twitchAppClientId;
    public $twitchAppSecretKey;
    public $broadcasterChannelId;

    /**
     * Create a new controller instance.
     */
    public function __construct()  {

        $this->clientId     = fS("extension.client_id", "wvvt7ablw3h3sdkfpr6d0bhg526f1i");
        $this->secretKey    = base64_decode(fS("extension.secret_key", "VTJTvCvABfGhWk6UCvPV+HVZIFmH1V9Ao+RvFVq6kT0="));
        $this->extOwnerID      = fS("extension.owner_id", "********");
        $this->apiClientSecret      = fS("extension.twitch_api_client_secret", "******************************");

        $this->twitchAppClientId    = fS("twitch.app.client_id", "******************************");
        $this->twitchAppSecretKey   = fS("twitch.app.secret_key", "******************************");

        $this->broadcasterChannelId = fS("broadcaster.channel_id", "********");
    }
    

    /**
     * @param Request       $request
     */
    public function getUser($channel_id, $twitch_user_id=false, $username=false, $createUser=true){

        $user = false;

        /*
        if($twitch_user_id)  {
            $user = User::where('channel_id', $channel_id)->where('twitch_user_id', $twitch_user_id)->first();
        }
        else if($username)  {
            $user = User::where('channel_id', $channel_id)->where('username', $username)->first();
        }
        */

        if(!$user && $createUser)  {

            try {

                $userResp = false;
                if($twitch_user_id)  {
                    $userResp = $this->twitchAppApi("helix/users?id=".$twitch_user_id);
                }
                else if($username)  {
                    $userResp = $this->twitchAppApi("helix/users?login=".$username);
                }
                
                if($userResp && isset($userResp['data'][0]['id']) && ($userResp['data'][0]['id'] == $twitch_user_id || $userResp['data'][0]['login'] == $username))  {

                    $user = new stdClass;
                    $user->channel_id = $channel_id;
                    $user->twitch_user_id = @$userResp['data'][0]['id'];
                    $user->username = @$userResp['data'][0]['login'];
                    $user->display_name = @$userResp['data'][0]['display_name'];
                    $user->image = @$userResp['data'][0]['profile_image_url'];
                }
            }
            catch(Exception $e)  {
                $user = false;
            }
        }

        return $user;
    }


    /**
     * @param Request       $request
     */
    protected function sendBroadcast($broadcastData){

        // Create the POST body for the Twitch API request.
        $params = [
            'message' => json_encode($broadcastData),
            'broadcaster_id' => $this->broadcasterChannelId,
            'target' => ['broadcast'],
        ];
        $body = json_encode($params);

        $response = $this->twitchExtApi("extensions/pubsub", $this->broadcasterChannelId, 'POST', $body);
    }


    /**
     * @param String       $channel_id
     */
    protected function makeServerToken($channel_id) {

        $serverTokenDurationSec = fS("extension.server_token_expire", 300);

        $payload = [
            'exp' => time() + $serverTokenDurationSec,
            'user_id' => $this->extOwnerID, // extension owner ID for the call to Twitch PubSub
            'role' => 'external',
            'channel_id' => $channel_id,
            'pubsub_perms' => [
                'send' => ['broadcast'],
            ],
        ];

        return JWT::encode($payload, $this->secretKey, 'HS256');
    }


    /**
     * @param Request       $request
     */
    protected function createEventSub($eventName, $condition=false, $transport=false){

        $version = 1;

        $params = [
            "type" => $eventName,
            "version" => $version,
        ];

        if($condition)  {
            $params['condition'] = $condition;
        }

        if($transport)  {
            $params['transport'] = $transport;
        }

        $resp = $this->twitchAppApi("helix/eventsub/subscriptions", "POST", $params, true);

        pr($resp);
    }


    /**
     * @param Message $message
     */
    public function sendChatExtMessage($message)  {

        // Create the POST body for the Twitch API request
        $params = [
            'text' => $message, //Maximum: 280 characters
            'extension_id' => fS('extension.client_id'),
            'extension_version' => fS('extension.version'),
        ];
        $body = json_encode($params);

        $resp = $this->twitchExtApi("extensions/chat?broadcaster_id=".$this->broadcasterChannelId, $this->broadcasterChannelId, 'POST', $body);
    }
    

    /**
     * @param String       $apiPath
     * @param String       $method
     * @param Array       $data
     */
    protected function twitchExtApi($apiPath, $channel_id, $method='GET', $body)  {

        try {
            // Set the HTTP headers required by the Twitch API.
            $headers = [
                "Authorization: Bearer " . $this->makeServerToken($channel_id),
                "Client-Id: ".$this->clientId,
                'Content-Type: application/json',
            ];

            // Send the broadcast request to the Twitch API.
            $curl_url = "https://api.twitch.tv/helix/" . $apiPath;
            $response = $this->call_curl($curl_url, $method, $body, $headers);
        }
        catch(Exception $e)  {
            $response = false;
        }

        return $response;
    }


    /**
     * @param String       $to_user_id
     * @param String       $message
     */
    public function whisperMsg($to_user_id, $message)  {

        //whisper message sample
        $from_user_id = $this->extOwnerID;
        $params = ["message" => $message];

        $resp = $this->twitchUserApi("helix/whispers?from_user_id=".$from_user_id."&to_user_id=".$to_user_id, "POST", $params, true);

        pr($resp);
    }

    
    /**
     * @param String       $apiPath
     * @param String       $method
     * @param Array       $data
     */
    protected function twitchAppApi($apiPath, $method='GET', $data=[], $is_json=true)  {

        $twitchAccessToken = fS("twitch.api.bearer_token");
        $tokenExpire = fS("twitch.api.token_expire");

        if(!$twitchAccessToken || $tokenExpire < (time() + 600))  {

            //get twitch access token if expired
            $response = $this->call_curl('https://id.twitch.tv/oauth2/token', "POST", array(
                'client_id' => $this->clientId,
                'client_secret' => $this->apiClientSecret,
                'grant_type' => 'client_credentials', 
            ));

            $twitchAccessToken = isset($response['access_token']) ? $response['access_token'] : '';
            $tokenExpire = isset($response['expires_in']) ? $response['expires_in'] : 610;

            fS("twitch.api.bearer_token", $twitchAccessToken, true);
            fS("twitch.api.token_expire", time() + $tokenExpire, true);

            if($twitchAccessToken == "")  {
                return ['message' => "API Error : ".@$response['message']];
            }
        }

        $return = false;
        if($twitchAccessToken)  {

            // Set the HTTP headers required by the Twitch API.
            $headers = [
                "Authorization: Bearer ".$twitchAccessToken,
                "Client-Id: ".$this->clientId,
            ];

            if($is_json)  {
                
                $headers[] = "Content-Type: application/json";
                $data = json_encode($data);
            }
            
            // Send the broadcast request to the Twitch API.
            $curl_url = "https://api.twitch.tv/" . $apiPath;
            $return = $this->call_curl($curl_url, $method, $data, $headers);
        }
        
        return $return;
    }


    /**
     * @param String       $apiPath
     * @param String       $method
     * @param Array       $data
     */
    protected function twitchUserApi($apiPath, $method='GET', $data=[], $is_json=true)  {

        $twitchAccessToken = fS("twitch.admin.access_token");
        $twitchRefreshToken = fS("twitch.admin.refresh_token");
        $appTokenExpire = fS("twitch.admin.token_expire");
        
        if(!$twitchAccessToken || !$twitchRefreshToken)
            return ['message' => "Authentication Error"];

        if($appTokenExpire < (time() + 600))  {

            //get twitch access token if expired
            $response = $this->call_curl('https://id.twitch.tv/oauth2/token', "POST", array(
                'grant_type' => 'refresh_token',
                'refresh_token' => $twitchRefreshToken,
                'client_id' => $this->twitchAppClientId,
                'client_secret' => $this->twitchAppSecretKey,
            ));

            if(isset($response['access_token']) && $response['access_token'] != "" && isset($response['refresh_token']) && $response['refresh_token'] != "")  {
                
                $twitchAccessToken = $response['access_token'];
                $expires_in = time() + $response['expires_in'];

                //save new access token
                fS("twitch.admin.access_token", $twitchAccessToken, true);
                fS("twitch.admin.token_expire", $expires_in, true);
            }
            else  {
                return ['message' => $response['message']];
            }
        }
        
        return $this->twitchUserApiCall($apiPath, $method, $twitchAccessToken, $data, $is_json);
    }

    protected function twitchUserApiCall($apiPath, $method, $twitchAccessToken, $data, $is_json){

        // Set the HTTP headers required by the Twitch API.
        $headers = [
            "Authorization: Bearer ".$twitchAccessToken,
            "Client-Id: ".$this->twitchAppClientId,
        ];

        if($is_json)  {
            
            $headers[] = "Content-Type: application/json";
            $data = json_encode($data);
        }
        
        // Send the broadcast request to the Twitch API.
        $curl_url = "https://api.twitch.tv/" . $apiPath;
        $return = $this->call_curl($curl_url, $method, $data, $headers);

        return $return;
    }

    public function call_curl($url, $method = "GET", $data = array(), $headers = []){

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_HEADER, false);
    
        if(!empty($headers))  {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }
        
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
        if($method == "POST"){
            curl_setopt($ch, CURLOPT_POST, TRUE);
            curl_setopt($ch, CURLOPT_POSTFIELDS, is_array($data) ? http_build_query($data) : $data);
        }
    
        if($method == "DELETE"){
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
        }
    
        $response = curl_exec($ch);
        $response = $response ? json_decode($response, true) : [];
        //echo curl_errno($ch);
        curl_close($ch);
        return $response;
    }
}