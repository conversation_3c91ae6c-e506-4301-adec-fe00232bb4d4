<?php

namespace App\Http\Controllers\Admin;

use DB;
use App\Code\GoogleAuthenticator;
use App\Http\Controllers\Controller;
use App\Framework\src\Http\Models\Admin;
use App\Framework\src\Http\Models\AdminPassword;
use App\Framework\src\Http\Models\Log;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;

class AdminController extends Controller{

    /**
     * AdminController constructor.
     */
    public function __construct()  {

        $this->middleware('ipcheck');
        $this->middleware('logincheck');
        $this->middleware('admincheck');
    }

    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function listAdmin(Request $request) {

        $data = [];
        $data['admins']      = Admin::selectRaw("*". AESdecypts(Admin::encryptableFields()))->get();
        $data['active_menu'] = 'admin';
        return view('admin.admin_list', $data);
    }

    /**
     * @param Request $request
     *
     * @return JSON
     */
    public function ajaxList(Request $request){

        $draw   = intval($_GET["draw"]);
        $start  = intval($_GET["start"]);
        $length = intval($_GET["length"]);
        $search = $_GET["search"];
        $order  = $_GET["order"];

        $search_where = "";
        if(isset($search['value']) && $search['value'] != "")  {
            
            $search_val   = sanitizeStr($search['value']);
            $search_val_e = DB::connection()->getPdo()->quote('%' . $search_val . '%');
            $search_where = "(username LIKE $search_val_e OR LOWER(CONVERT(".AESdecypt('email', false)." USING utf8)) LIKE $search_val_e)";
        }

        //get count
		$admins = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()));
		if($search_where){
            $admins = $admins->whereRaw($search_where);
        }
        $total_count = $admins->count();

        //get order by
        $sort_cols = array(1 => "id", 2 => "username", 3 => "email", 4 => "user_type", 5 => "ga_secret", 6 => "created_at");
        if(in_array($order[0]['column'], array_keys($sort_cols))){
            $sort_col   = $order[0]['column'];
            $sort_order = $order[0]['dir'];
        }

        //get admins
        $admins = $admins->orderBy($sort_cols[$sort_col], $sort_order)->orderBy('id','desc')->take($length)->skip($start);
        $admins = $admins->get();

        //table data
        $data = array();
        foreach($admins as $admin){

            $actionData = '<div class="hstack gap-2 justify-content-end">';
            $actionData .= '<a href="'.route('admin_edit', ["id" => $admin->id]).'" class="btn btn-primary"><i class="feather-edit me-2"></i><span>Edit</span></a>';
            $actionData .= '<a href="'.route("admin_regenerate", ["id" => $admin->id]).'" class="btn btn-light">(Re) Generate</a>';
            $actionData .= '<a data-record-id="'.$admin->id.'" class="btn btn-danger" data-kt-record-table-filter="delete_row"><i class="feather-trash-2 me-2"></i><span>Delete</span></a>';
            $actionData .= '</div>';

            $data[] = array(
                '<div class="item-checkbox ms-1">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input checkbox del-check-input" id="checkBox_'.$admin->id.'" value="'.$admin->id.'">
                        <label class="custom-control-label" for="checkBox_'.$admin->id.'"></label>
                    </div>
                </div>',
                $admin->id,
                $admin->username,
                $admin->email,
                ucwords(str_replace("_", " ", $admin->user_type)),
                ($admin->qrcode ? '<a href="'.$admin->qrcode.'" target="_blank"><img src="'.$admin->qrcode.'" height="100"></a>' : ''),
                date('d.M.Y, h:i A', strtotime($admin->created_at)),
                $actionData,
            );
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $total_count,
            "recordsFiltered" => $total_count,
            "data"            => $data
        );
        echo json_encode($output);
    }

    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function newAdmin(Request $request){

        $data = [];
        $data['active_menu'] = 'admin';

        return view('admin.admin_form', $data);
    }

    /**
     * @param Request $request
     * @param string  $id
     *
     * @return Application|Factory|RedirectResponse|View
     */
    public function editAdmin(Request $request, $id = ''){

        $id    = (int)sanitizeStr($id);
        $admin = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('id', $id)->first();
        if(!$admin)
            return redirect()->route("admin_list_admin");

        $data['admin']       = $admin;
        $data['admin_id']    = $id;
        $data['active_menu'] = 'admin';
        return view('admin.admin_form', $data);
    }

    /**
     * @param Request $request
     * @param string  $id
     *
     * @return RedirectResponse
     */
    public function saveAdmin(Request $request, $id = '')  {

        $id = (int)sanitizeStr($id);
        if($id)  {

            $admin = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('id', $id)->first();

            $validateArr = [];

            if($request->password)  {
                $validateArr['password'] = 'required|Min:8|Max:20|regex:/^.*(?=.{3,})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[\s\S].*$/';
            }

            if(!empty($validateArr))  {
                $request->validate($validateArr);
            }
            
            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".update", ["id" => $id]);
        }
        else
        {
            $validateArr = array(
                'username' => 'required|unique:admins|alpha_num',
                'email'    => 'required|unique:admins|email',
                'password' => 'required|Min:8|Max:20|regex:/^.*(?=.{3,})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[\s\S].*$/',
            );

            $request->validate($validateArr);

            $admin           = new Admin;
            $admin->username = sanitizeStr($request->get('username'));
            $admin->email    = strtolower(sanitizeStr($request->get('email')));
            
            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".insert", ["id" => $id]);
        }

        if($request->password)
            $admin->password = Hash::make($request->password);

        $admin->user_type = sanitizeStr($request->get('user_type'));
        $admin->user_roles = $request->get('user_roles') ? sanitizeStr(implode(",", $request->get('user_roles'))) : "";
        $admin->save();
        
        return redirect()->route("admin_list_admin");
    }

    /**
     * @param $password
     * @param $admin_id
     */
    function savePassword($password, $admin_id){

        $adminPassword           = new AdminPassword;
        $adminPassword->admin_id = $admin_id;
        $adminPassword->password = $password;
        $adminPassword->save();
    }

    /**
     * @param Request $request
     * @param         $id
     *
     * @return RedirectResponse
     */
    public function deleteAdmin(Request $request){

        $id = (int)sanitizeStr($request->post('record_id'));
        if($id > 0)  {
            Admin::destroy($id);

            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".destroy", ["id" => $id]);
        }

        $record_ids = sanitizeStr($request->post('record_ids'));

        if($record_ids)  {

            $recordIds = explode(",", $record_ids);

            foreach($recordIds as $recordId)  {

                $id = (int)sanitizeStr($recordId);
                if($id > 0)  {

                    AdminPassword::where('admin_id', $id)->delete();

                    Admin::destroy($id);
                }
            }

            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".destroy", ["id" => $record_ids]);
        }

        return '1';
    }

    
    /**
     * @param Request $request
     * @param         $id
     *
     * @return RedirectResponse
     */
    public function regenerate(Request $request, $id) {
        
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".refresh", ["id" => $id]);
        $id               = intval(sanitizeStr($id));
        $admin            = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('id', $id)->first();
        $ga               = new GoogleAuthenticator();
        $admin->ga_secret = $ga->createSecret();
        $admin->qrcode    = $ga->getQRCodeGoogleUrl(config("app.google_auth_identifier") . "_" . slugify_string($admin->username), $admin->ga_secret);
        $admin->save();
        return redirect()->route("admin_list_admin");
    }
}
