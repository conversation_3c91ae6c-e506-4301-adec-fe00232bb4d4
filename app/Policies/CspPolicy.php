<?php

namespace App\Policies;

use <PERSON><PERSON>\Csp\Directive;
use <PERSON>tie\Csp\Keyword;
use <PERSON>tie\Csp\Policies\Basic;

class CspPolicy extends Basic{
    const RELAXED_URL_PATTERNS = [
        //'*', // Use for all
        //'*.*',
        'admin/*',
        'admin',
        'deploy/*',
        'stream/*',
    ];

    const ALLOWED_URL_PATTERNS_IMG = [
        "self",
        "data:",
        "blob:",
        "*.evidon.com",
        "static-cdn.jtvnw.net",
        "*.playstation.com",
        "*.playstation.net",
        "*.googleusercontent.com",
    ];

    const ALLOWED_URL_PATTERNS_FONT = [
        "self",
        "data:",
        "blob:",
    ];

    const ALLOWED_URL_PATTERNS_STYLE = [
        "self",
        "data:",
        "blob:",
        "*.google.com"
    ];

    const ALLOWED_URL_PATTERNS_SCRIPT = [
        "self",
        "data:",
        "blob:",
        "www.gstatic.com",
        "www.google.com",
    ];

    protected function shouldUseRelaxed(){
        foreach(static::RELAXED_URL_PATTERNS as $pattern)
            if(request()->is($pattern)) return true;
        return false;
    }

    public function configure(){
        // Use relaxed mode for certain urls
        $useRelaxedRules = $this->shouldUseRelaxed();

        // Build csp directives
        if(!$useRelaxedRules){
            $this->addDirective(Directive::STYLE, static::ALLOWED_URL_PATTERNS_STYLE)
                 ->addDirective(Directive::IMG, static::ALLOWED_URL_PATTERNS_IMG)
                 ->addDirective(Directive::FONT, static::ALLOWED_URL_PATTERNS_FONT)
                 ->addDirective(Directive::SCRIPT, static::ALLOWED_URL_PATTERNS_SCRIPT)
                 ->addDirective(Directive::SCRIPT, Keyword::STRICT_DYNAMIC)
                 ->addNonceForDirective(Directive::SCRIPT)
                 ->addDirective(Directive::FRAME_ANCESTORS, "self");
        }
    }
}
