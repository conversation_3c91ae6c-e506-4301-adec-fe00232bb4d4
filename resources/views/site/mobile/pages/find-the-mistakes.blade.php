<span class="cc-section-bg position-fixed">
    <img loading="lazy" src="{{ url('public') }}/gfx/detail-bg.jpg" alt="image of detail BG">
</span>
<div class="cc-page-inner">
    <div class="cc-page-content">
        <div class="db-body-content">
            <div class="db-body-component pb-0">

                @include("site.mobile.elements.game-tabs")
                
                <div class="cc-find-the-mistakes-heading text-a">
                    @if(!$ticket)
                        <h2 class="h3">{!! fT('d_pages.a_find_the_mistake.a_mistakes', 'Mistakes:') !!} 0 of 3</h2>
                    @endif    
                </div>
                <div class="cc-find-the-mistakes-wrap">
                    <div class="cc-find-the-mistakes-img {{ ($ticket) ? 'done' : '' }}">
                        <div class="cc-find-the-mistakes-thumb ratio">
                            <img decoding="async" loading="lazy" src="{{ ccImgUrl($findTheMistake->image_mobile, "orginal-img.jpg") }}" alt="image of orginal">
                        </div>
                        <div class="cc-find-the-mistakes-thumb ratio" id="mistakeArea">
                            <img decoding="async" class="{{ ($ticket) ? 'done' : '' }}" id="mistakeImg" loading="lazy" src="{{ ccImgUrl($findTheMistake->mistake_image_mobile, "mistakes-image.jpg") }}" alt="image of mistakes">
                        </div>

                    </div>
                    @if($ticket)
                        @php
                            $icon = ($ticket->tickets > 0) ? 'check' : 'close';
                        @endphp
                        <span class="cc_mg_game-done-icon"><i class="cc_icon-{{$icon}}-2"><svg role="img"><title>{{$icon}}-2 icon</title><use xlink:href="#cc_icon-{{$icon}}-2"></use></svg></i></span>
                    @endif
                </div>
            </div>

            @if(!$ticket)
                <form name="findTheMistake-detail-form" id="ak-findTheMistake-form" method="post">
                    {{ csrf_field() }}
                    <input type="hidden" name="code" value="{{ $findTheMistake->code }}" />
                </form>
            @endif

            <div class="db-body-bottom">
                <div class="cc-bt-description text-a">
                    {!! auto_ptag(fT('d_pages.a_find_the_mistake.b_description', '<p>Torem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis.</p>')) !!}
                </div>
            </div>
        </div>
    </div>
</div>