"use strict";
$(function() {
    $(document).on("click", ".cutomizer-open-trigger", function(a) {
        a.preventDefault(), $(".theme-customizer").addClass("theme-customizer-open")
    }), $(document).on("click", ".cutomizer-close-trigger", function(a) {
        a.preventDefault(), $(".theme-customizer").removeClass("theme-customizer-open")
    })
}), $(function() {
    void 0 === window.localStorage && alert("Your browser is outdated!");
    var a = localStorage["app-navigation"];
    a && ($("html").addClass(a), $('[name="app-navigation"][data-app-navigation="' + a + '"]').prop("checked", !0)), $('[data-app-navigation="app-navigation-light"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-navigation-light").removeClass("app-navigation-dark"), localStorage.setItem("app-navigation", a.data("app-navigation"))
    }), $('[data-app-navigation="app-navigation-dark"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-navigation-dark").removeClass("app-navigation-light"), localStorage.setItem("app-navigation", a.data("app-navigation"))
    }), $("[data-style='reset-app-navigation']").on("click", function(a) {
        a.preventDefault(), location.reload(!0), window.localStorage.removeItem("app-navigation")
    })
}), $(function() {
    void 0 === window.localStorage && alert("Your browser is outdated!");
    var a = localStorage["app-header"];
    a && ($("html").addClass(a), $('[name="app-header"][data-app-header="' + a + '"]').prop("checked", !0)), $('[data-app-header="app-header-light"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-header-light").removeClass("app-header-dark"), localStorage.setItem("app-header", a.data("app-header"))
    }), $('[data-app-header="app-header-dark"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-header-dark").removeClass("app-header-light"), localStorage.setItem("app-header", a.data("app-header"))
    }), $("[data-style='reset-app-header']").on("click", function(a) {
        a.preventDefault(), location.reload(!0), window.localStorage.removeItem("app-header")
    })
}), $(function() {
    void 0 === window.localStorage && alert("Your browser is outdated!");
    var a = localStorage["app-skin"];
    a && ($("html").addClass(a), $('[name="app-skin"][data-app-skin="' + a + '"]').prop("checked", !0)), $('[data-app-skin="app-skin-light"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").removeClass("app-skin-dark"), localStorage.setItem("app-skin", a.data("app-skin"))
    }), $('[data-app-skin="app-skin-dark"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-skin-dark").removeClass("app-skin-light"), localStorage.setItem("app-skin", a.data("app-skin"))
    }), $("[data-style='reset-app-skin']").on("click", function(a) {
        a.preventDefault(), location.reload(!0), window.localStorage.removeItem("app-skin")
    })
}), $(function() {
    var a = localStorage.getItem("app-skin-dark") ? localStorage.getItem("app-skin-dark") : "app-skin-light";
    localStorage.setItem("app-skin-dark", a), "app-skin-dark" == localStorage.getItem("app-skin-dark") && $("html").addClass("app-skin-dark") && $(".dark-button").addClass("active") && $(".light-button").removeClass("active"), $(".dark-button").on("click", function(a) {
        a.preventDefault(), $(".dark-button").hide().addClass("active"), $(".light-button").show().removeClass("active"), $("html").addClass("app-skin-dark"), localStorage.setItem("app-skin-dark", "app-skin-dark")
    }), $(".light-button").on("click", function(a) {
        a.preventDefault(), $(".light-button").hide().addClass("active"), $(".dark-button").show().removeClass("active"), $("html").removeClass("app-skin-dark"), localStorage.setItem("app-skin-dark", "app-skin-light")
    }), $("[data-style='reset-app-skin']").on("click", function(a) {
        a.preventDefault(), location.reload(!0), window.localStorage.removeItem("app-skin-dark")
    })
}), $(function() {
    void 0 === window.localStorage && alert("Your browser is outdated!");
    var a = localStorage["font-family"];
    a && ($("html").addClass(a), $('[name="font-family"][data-font-family="' + a + '"]').prop("checked", !0)), $('[data-font-family="app-font-family-lato"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-lato").removeClass("app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-rubik"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-rubik").removeClass("app-font-family-lato app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-inter"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-inter").removeClass("app-font-family-lato app-font-family-rubik app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-cinzel"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-cinzel").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-nunito"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-nunito").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-roboto"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-roboto").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-ubuntu"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-ubuntu").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-poppins"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-poppins").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-raleway"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-raleway").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-system-ui"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-system-ui").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-noto-sans"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-noto-sans").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-fira-sans"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-fira-sans").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-work-sans"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-work-sans").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-open-sans"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-open-sans").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-maven-pro"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-maven-pro").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-quicksand"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-quicksand").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro  app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-montserrat"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-montserrat").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-josefin-sans"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-josefin-sans").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-ibm-plex-sans"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-ibm-plex-sans").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-source-sans-pro"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-source-sans-pro").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-montserrat-alt"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-montserrat-alt").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-roboto-slab"), localStorage.setItem("font-family", a.data("font-family"))
    }), $('[data-font-family="app-font-family-roboto-slab"]').on("change", function(a) {
        a.preventDefault();
        a = $(this);
        $("html").addClass("app-font-family-roboto-slab").removeClass("app-font-family-lato app-font-family-rubik app-font-family-inter app-font-family-cinzel app-font-family-source-sans-pro app-font-family-nunito app-font-family-roboto app-font-family-ubuntu app-font-family-poppins app-font-family-raleway app-font-family-system-ui app-font-family-noto-sans app-font-family-fira-sans app-font-family-work-sans app-font-family-open-sans app-font-family-maven-pro app-font-family-quicksand app-font-family-montserrat app-font-family-josefin-sans app-font-family-ibm-plex-sans app-font-family-montserrat-alt"), localStorage.setItem("font-family", a.data("font-family"))
    }), $("[data-style='reset-font-family']").on("click", function(a) {
        a.preventDefault(), location.reload(!0), window.localStorage.removeItem("font-family")
    })
}), $(function() {
    void 0 === window.localStorage && alert("Your browser is outdated!"), $("[data-style='reset-all-common-style']").on("click", function(a) {
        a.preventDefault(), window.localStorage.removeItem("app-navigation"), window.localStorage.removeItem("app-header"), window.localStorage.removeItem("app-skin-dark"), window.localStorage.removeItem("font-family"), location.reload(!0)
    })
});