<?php

namespace Framework\plugins\asset_optimizer\src\Providers;

use Framework\plugins\asset_optimizer\migrations\AssetOptimizerMigration;
use Framework\plugins\asset_optimizer\src\Code\ImageOptimizer;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider as BaseServiceProvider;

class ServiceProvider extends BaseServiceProvider implements DeferrableProvider{
    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides(): array{
        return [
            ImageOptimizer::class
        ];
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register(): void{
        $this->app->singleton(ImageOptimizer::class, function (){
            AssetOptimizerMigration::ensureSchemaStructure();
            return new ImageOptimizer();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(): void{

    }
}
