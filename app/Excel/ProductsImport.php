<?php
namespace App\Excel;

use App\Http\Models\GameCatalogue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class ProductsImport implements ToCollection  {

    
	/**
     * @param array $rows
    */
	public function collection(Collection $rows)  {

		//validation
		if(trim($rows[0][0]) != 'Product ID') {
			exit('Wrong first column name');
		}

        //import records
        $game_product_ids = [];
        foreach($rows as $row)  {

            if(trim($row[0]) && $row[0] != 'Product ID')  {

                $game_id = sanitizeStr($row[0]);

                $gameCatalogue = GameCatalogue :: where('game_id', $game_id)->first();
                
                if(!$gameCatalogue)  {

                    $gameCatalogue = new GameCatalogue();
                    array_push($game_product_ids, $game_id);
                }
                
                $gameCatalogue->game_id    = $game_id;
                $gameCatalogue->code       = random_string(16);
                $gameCatalogue->save();
            }
        }

        //api call for game name
        if($game_product_ids)  {

            $api_url = 'https://sied02.contentcreators.at/api/product/list-detail';
            $api_token = '2|WR5HexBNeAfzM5FM5TJsoWDfQj8Qb8mFS4QYmMk7';
            
            $batchSize = 10;
            $gameProductIdsChunks = array_chunk($game_product_ids, $batchSize, true);

            foreach($gameProductIdsChunks as $gameProductIdsChunk)  {

                $gameProductIds = implode(",", $gameProductIdsChunk);
    
                $api_url = $api_url."?token=".$api_token."&fields=ps_id,name,concept_name,normal_price,discount_price&ps_ids=" . $gameProductIds;
                
                $game_products = call_curl($api_url);
    
                if($game_products)  {
                    
                    $game_products = json_decode($game_products, true);
    
                    if(isset($game_products['data']) && !empty($game_products['data']))  {
    
                        foreach($game_products['data'] as $game_product)  {
    
                            $gameCatalogue = GameCatalogue :: where('game_id', $game_product['ps_id'])->first();

                            if($gameCatalogue)  {

                                $gameCatalogue->game_name = $game_product['concept_name'];
                                $gameCatalogue->price = $game_product['discount_price'] / 100;
                                $gameCatalogue->save();
                            }
                        }
                    }
                }
            }
        }    
    }
}