@php
$id = $id ?? $name;
$id = str_replace("[", "-", $id);
$id = str_replace("]", "", $id);

$error_name = str_replace("]", "", str_replace("[", ".", $name));
@endphp

<div class="mb-4 cc-form-field-include {{ $additionalClass ?? "" }}">

    @if(isset($label) && $label)

        <label for="{{ $id }}" class="form-label">
            {!! $label !!} 
            
            @if(isset($required) && $required)
                <span class="text-danger">*</span>
            @endif

        </label>
        
    @endif

    
    <select autocomplete="off" class="form-control" name="{{ $name }}" id="{{ $id ?? $name }}"
        @if(isset($required) && $required) data-validation="required" @endif
        @if(isset($disabled) && $disabled) disabled @endif>
        @if(isset($optionNoneSelected) && $optionNoneSelected)
            <option value="">{{ $optionNoneSelected ?? "-" }}</option>
        @endif
        
        @foreach($options as $val => $lab)
            <option value="{{ $val }}" @if(isset($value) && $value == $val) selected="selected" @endif>
                {!! $lab !!}
            </option>
        @endforeach
    </select>

    @if(isset($disabled) && $disabled)
        <input type="hidden" name="{{ $name }}" value="{{ $value }}">
    @endif

    @isset($info)
        <small id="{{ $id ?? $name . "_info" }}" class="form-text text-muted">{{ $info }}</small>
    @endisset

    @if ($errors->has($error_name))
        <label id="{{ $id ?? $name }}-error" class="error" for="{{ $id }}">
            @foreach($errors->get($error_name) as $error)
                {{ $error }} <br>
            @endforeach
        </label>
    @endif

</div>