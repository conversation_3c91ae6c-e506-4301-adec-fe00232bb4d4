@extends('admin.layouts.layout')
@section('content')

    @section('custom_css')
    <link href="{{ asset('public/assets') }}/vendors/css/dataTables.bs5.min.css" rel="stylesheet" type="text/css" />
    @endsection

    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">Transactions</h5>
                </div>
            </div>
            <div class="page-header-right ms-auto">
                <div class="page-header-right-items">
                    <div class="d-flex d-md-none">
                        <a href="javascript:void(0)" class="page-header-right-close-toggle">
                            <i class="feather-arrow-left me-2"></i>
                            <span>Back</span>
                        </a>
                    </div>
                    <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                        <a href="{{ route("admin_transaction_edit") }}" class="btn btn-md btn-primary">
                            <i class="feather-plus me-2"></i>
                            <span>Create Transaction</span>
                        </a>
                    </div>
                </div>
                <div class="d-md-none d-flex align-items-center">
                    <a href="javascript:void(0)" class="page-header-right-open-toggle">
                        <i class="feather-align-right fs-20"></i>
                    </a>
                </div>
            </div>
        </div>
        <!-- [ page-header ] end -->
        <!-- [ Main Content ] start -->
        
        <div class="main-content">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card stretch stretch-full">
                        <div class="card-header card-channel-head">
                            <div class="card-header-action">
                                <div class="d-flex justify-content-end align-items-center d-none" data-kt-record-table-toolbar="selected">
                                    <div class="fw-bold me-5">
                                    <span class="me-2" data-kt-record-table-select="selected_count"></span>Selected</div>
                                    <button type="button" class="btn btn-danger" data-kt-record-table-select="delete_selected">Delete Selected</button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            
                            <div class="table-responsive">
                                <table class="table table-hover datatable_list" id="transaction_list" data-ajax-url="{{ route('admin_ajax_transaction_list') }}" data-sort-col="1" data-sort-order="asc" data-record-name="0">
                                    <thead>
                                        <tr>
                                            <th class="wd-30" data-orderable="false">
                                                <div class="btn-group mb-1">
                                                    <div class="custom-control custom-checkbox ms-1">
                                                        <input class="custom-control-input del-check-input-all" id="transaction_list_check" type="checkbox" value="1" />
                                                        <label class="custom-control-label" for="transaction_list_check"></label>

                                                    </div>
                                                </div>
                                            </th>
                                            <th>Name</th>
                                            <th>Title</th>
                                            <th data-orderable="false">Body</th>
                                            <th class="text-end" data-orderable="false">Actions</th>

                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                    </tbody>
                                </table>
                            </div>
                            
                        </div>
                    </div>
                </div>


            </div>

        </div>
        
        <!-- [ Main Content ] end -->
    </div>

    <form action="{{ route('admin_transaction_remove') }}" method="post" id="record-delete-form" class="d-none">
        {!! csrf_field() . method_field('DELETE') !!}
        <input type="hidden" name="record_id" value="">
        <input type="hidden" name="record_ids" value="">
        <button type="submit" class="btn-delete">Delete</button>
    </form>

    @section('custom_javascript')
        <script src="{{ asset('public/assets') }}/vendors/js/dataTables.min.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/dataTables.bs5.min.js"></script>
    @endsection

@endsection