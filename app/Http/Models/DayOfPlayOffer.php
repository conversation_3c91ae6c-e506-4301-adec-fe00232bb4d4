<?php
namespace App\Http\Models;

use Eloquent;
use Exception;
use App\Framework\src\Code\EzCache;
use App\Framework\src\Http\Traits\ManageableModelTrait;
use Illuminate\Contracts\Cache\Repository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @property integer id
 * @property string  product_name
 * @property decimal sales_prize
 * @property string  image
 * @property string  offer_link
 * @property integer sort_order

 * @property string  created_at
 * @property string  updated_at
 *
 * @mixin Builder
 * @mixin Eloquent
 */
class DayOfPlayOffer extends Model{
	use ManageableModelTrait;

    protected $table   = 'day_of_play_offers';
    protected $guarded = [];
	
    protected static $NON_ID_FIELDS = [
		
        "product_name"      => ["type" => "string", "length" => 256, "null" => true],
        "sales_prize"       => ["type" => "decimal", "null" => true],
        "image"             => ["type" => "string", "length" => 100, "null" => true],
        "mobile_image"      => ["type" => "string", "length" => 100, "null" => true],
        "offer_link"        => ["type" => "string", "length" => 255, "null" => true],
        "sort_order"        => ["type" => "integer", "default" => 9999],
        
        // ---
        "created_at" => ["type" => "created_at", "null" => true],
        "updated_at" => ["type" => "updated_at"],
    ];

    /**
     * @return Repository|mixed
     * @throws Exception
     */
    public static function getAllSorted()  {

		$cacheKey = "day_of_play_offers";
		
        return  EzCache::get($cacheKey, static function ()  {

            $records_sorted   = DayOfPlayOffer::orderBy("sort_order", 'ASC')->get();
            return $records_sorted;
        });
    }
}