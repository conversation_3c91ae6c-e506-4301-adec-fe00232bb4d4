<?php

namespace App\Http\Models;

use App\Framework\src\Http\Traits\ManageableModelTrait;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * @property integer id
 * @property string  key
 * @property string  value
 *
 * @mixin Builder
 * @mixin Eloquent
 */
class StatsData extends Model{
    use ManageableModelTrait;

    public    $timestamps = false;
    protected $table      = 'app_stats';
    protected $guarded    = [];

    protected static $NON_ID_FIELDS = [
        "key"   => ["type" => "string", "index" => true],
        "day"   => ["type" => "date", "index" => true],
        "value" => ["type" => "medium_text", "default" => ""],
    ];

    /**
     * @return int|mixed|null
     */
    public static function getStat($key, $day){
        $statRow = DB::table("app_stats")->select("value")->where("key", $key)->where("day", $day)->first();
        return $value = $statRow ? $statRow->value : 0;
    }

    /**
     * @return int|mixed|null
     */
    public static function incrementStat($key, $incrementVal = 1, $day = false){
        $day = $day !== false ? $day : get_calendar_date();

        $lockKey = $day . "_" . $key;
        return advisoryLock($lockKey, 1, static function () use ($day, $key, $incrementVal){
            $statRow = StatsData::where("key", $key)->where("day", $day)->first();
            if($statRow){
                $statRow->value += $incrementVal;
            }
            else{
                $statRow        = new StatsData();
                $statRow->key   = $key;
                $statRow->day   = $day;
                $statRow->value = $incrementVal;
            }
            $statRow->save();
            return $statRow->value;
        });
    }

}
