<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ValidateSessionIntegrity  {

    public function handle(Request $request, Closure $next)  {
        
        $userAgent = $request->header('User-Agent');
        $ipAddress = $request->ip();

        $sessionAgent = session('session_user_agent');
        $sessionIp = session('session_ip_address');

        if (!$sessionAgent || !$sessionIp) {

            // First login — store values
            session([
                'session_user_agent' => $userAgent,
                'session_ip_address' => $ipAddress,
            ]);
        }
        elseif ($sessionAgent !== $userAgent || $sessionIp !== $ipAddress) {
            
            // Session hijack detected
            $request->session()->invalidate();
            $request->session()->flush();
            $request->session()->regenerateToken();
        }
        
        return $next($request);
    }
}