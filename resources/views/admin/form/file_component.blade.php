<div class="form-group cc-form-field-include {{ $additionalClass ?? "" }}" id="{{ $id ?? "" }}_ctn">
    <button type="button" class="btn btn-info btn-sm btn-simple-file-upload-btn" id="{{ $id ?? "" }}_btn"
            data-input_id="{{ $id ?? "" }}">
        {{ $buttonText ?? "" }}
    </button>
    <span class="ml-2" id="{{ $id ?? "" }}_filename"></span>
    <input type="file" name="{{ $name }}" value="" class="simple-file-upload-input" id="{{ $id ?? "" }}"
           accept="{{ $accept ?? ".jpg,.jpeg,.png" }}" style="display:none"/>
    
    @if ($errors->has($error_name))
        <label id="{{ $id ?? $name }}-error" class="error" for="{{ $id }}">
            @foreach($errors->get($error_name) as $error)
                {{ $error }} <br>
            @endforeach
        </label>
    @endif

</div>
