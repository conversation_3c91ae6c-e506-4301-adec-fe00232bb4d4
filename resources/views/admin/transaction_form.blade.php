@extends('admin.layouts.layout')
@section('content')

    @section('custom_css')
    @endsection

    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">Transaction</h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route("admin_transactions") }}">Back</a></li>
                    <li class="breadcrumb-item">{{ @$transaction_id ? 'Update' : 'Create' }}</li>
                </ul>
            </div>
            <div class="page-header-right ms-auto">
                <div class="page-header-right-items">
                    <div class="d-flex d-md-none">
                        <a href="javascript:void(0)" class="page-header-right-close-toggle">
                            <i class="feather-arrow-left me-2"></i>
                            <span>Back</span>
                        </a>
                    </div>
                    <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                        <a href="javascript:void(0);" class="btn btn-primary cc-form-submit-btn">
                            <i class="feather-save me-2"></i>
                            <span>Save</span>
                        </a>
                    </div>
                </div>
                <div class="d-md-none d-flex align-items-center">
                    <a href="javascript:void(0)" class="page-header-right-open-toggle">
                        <i class="feather-align-right fs-20"></i>
                    </a>
                </div>
            </div>
        </div>
        <!-- [ page-header ] end -->
        <!-- [ Main Content ] start -->
        <div class="main-content">
            <form id="kt_transaction_form" class="form" action="{{ route("admin_transaction_save", ["id" => @$transaction_id ?: ""]) }}" method="POST">
                <div class="row">
                    <div class="col-xl-12">
                        
                        <div class="card stretch stretch-full">
                            <div class="card-body">
                                
                                @if (session('suc_message'))
                                    <div class="alert alert-success">{{session('suc_message')}}</div>
                                @endif

                                @if (session('err_message'))
                                    <div class="alert alert-danger">{{session('err_message')}}</div>
                                @endif

                                <div class="row">

                                    @include('admin.form.text_component',  ['name' => 'name', 'value' => (old('name') ?: (@$name ?: "")), 'label' => "Transaction Name (Internal)", 'required' => true])
                                            
                                    @include('admin.form.text_component',  ['name' => 'title', 'value' => (old('title') ?: (@$title ?: "")), 'required' => false, 'label' => "Title <small>([NAME] [LABEL] [TICKETS] [PRICE_NAME])</small>"])
                                    
                                    @include('admin.form.textarea_component',  ['name' => 'body_text', 'value' => (old('body_text') ?: (@$body_text ?: "")), 'required' => true, 'label' => "Body Text <small>([NAME] [LABEL] [TICKETS] [PRICE_NAME])</small>"])
                                    
                                    @include('admin.form.ajax_image_upload_component',  ['name' => 'image', 'value' => (old('image') ?: (@$image ?: "")), 'label' => "Image" , 'required' => false ])
    
                                </div>        
                                
                            </div>
                        </div>
                    </div>
                </div>

                @if(@$transaction_id)
                    <input type="hidden" name="transaction_id" value="{{ $transaction_id }}">
                @endif

                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                <button type="submit" class="d-none cc-form-submit">Submit</button>
            </form>    
        </div>
        <!-- [ Main Content ] end -->
    </div>

    @section('custom_javascript')
    @endsection

@endsection