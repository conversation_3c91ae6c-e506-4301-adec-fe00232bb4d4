<?php

namespace App\Framework\src\Http\Models;

use App\Framework\src\Http\Traits\ManageableModelTrait;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Locale;

/**
 * @property integer id
 * @property integer language_id
 * @property string  code
 * @property string  name
 * @property string  created_at
 * @property string  updated_at
 *
 * @mixin Builder
 * @mixin Eloquent
 */
class Country extends Model{
    use ManageableModelTrait;

    protected        $table         = 'countries';
    protected        $guarded       = [];
    protected static $NON_ID_FIELDS = [
        "language_id" => ["type" => "bigint", "unsigned" => true, "index" => true, "null" => true],
        "code"        => ["type" => "string", "length" => 20, "index" => true, "null" => true],
        "name"        => ["type" => "string", "length" => 200, "null" => true],
        // ---
        "created_at"  => ["type" => "created_at", "null" => true],
        "updated_at"  => ["type" => "updated_at", "null" => true],
    ];

    public function fillOut(): void{
        $languages = Language::all();
        foreach($languages as $language){
            $countryCode = Str::of($language->code)->explode("_")->map("trim")->last(); // DE
            $name        = @Locale::getDisplayRegion("-" . $countryCode, "en");
            Country::updateOrCreate([
                "language_id" => $language->id,
                "code"        => $countryCode,
            ], [
                "language_id" => $language->id,
                "code"        => $countryCode,
                "name"        => $name,
            ]);
        }
    }
}
