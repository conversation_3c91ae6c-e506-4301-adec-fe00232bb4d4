<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Framework\src\Http\Models\DataRevision;
use App\Framework\src\Http\Models\Log;
use App\Http\Models\Trivia;
use App\Http\Requests\TriviaSaveRequest;
use DB;
use Illuminate\Http\Request;

class TriviaController extends Controller{

    /**
     * TriviaController constructor.
     *
     * @param Request $request
     */
    public function __construct(Request $request){
        $this->middleware('ipcheck');
        $this->middleware('logincheck:trivia');
    }


    /**
     * @param Request $request
     *
     * @return Factory|View
     */
    public function index(Request $request){

        $data['active_menu'] = 'trivia';
        return view('admin.trivias', $data);
    }


    /**
     * @param Request $request
     *
     * @return JSON
     */
    public function ajaxList(Request $request){

        $draw   = intval($_GET["draw"]);
        $start  = intval($_GET["start"]);
        $length = intval($_GET["length"]);
        $search = $_GET["search"];
        $order  = $_GET["order"];

        $search_where = "";
        if(isset($search['value']) && $search['value'] != ""){
            $search_val   = sanitizeStr($search['value']);
            $search_val_e = DB::connection()->getPdo()->quote('%' . $search_val . '%');
            $search_where = "(question LIKE $search_val_e OR quiz_title LIKE $search_val_e)";
        }

        //get count
        $trivias     = $search_where ? Trivia::whereRaw($search_where)->get() : Trivia::get();
        $total_count = $trivias->count();

        //get order by
        $sort_cols = array( 1 => "day", 2 => "quiz_title", 3 => "question");
        if(in_array($order[0]['column'], array_keys($sort_cols))){
            $sort_col   = $order[0]['column'];
            $sort_order = $order[0]['dir'];
        }

        //get trivias
        $trivias = Trivia::orderBy($sort_cols[$sort_col], $sort_order)->take($length)->skip($start);
        if($search_where){
            $trivias = $trivias->whereRaw($search_where);
        }
        $trivias = $trivias->get();


        //table data
        $data = array();
        foreach($trivias as $trivia){

            $actionData = '<div class="hstack gap-2 justify-content-end">';  // class="text-end"
            $actionData .= '<a href="'.route('admin_trivia_edit', ["id" => $trivia->id]).'" class="btn btn-primary"><i class="feather-edit me-2"></i><span>Edit</span></a>';
            $actionData .= '<a data-record-id="'.$trivia->id.'" class="btn btn-danger" data-kt-record-table-filter="delete_row"><i class="feather-trash-2 me-2"></i><span>Delete</span></a>';
            $actionData .= '</div>';

            $data[] = array(
                '<div class="item-checkbox ms-1">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input checkbox del-check-input" id="checkBox_'.$trivia->id.'" value="'.$trivia->id.'">
                        <label class="custom-control-label" for="checkBox_'.$trivia->id.'"></label>
                    </div>
                </div>',
                date('d.m.Y', strtotime($trivia->day)),
                $trivia->quiz_title,
                $trivia->question,
                $actionData,
            );
        }
		
        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $total_count,
            "recordsFiltered" => $total_count,
            "data"            => $data
        );
        echo json_encode($output);
    }


    /**
     * @param Request $request
     * @param         $id
     *
     * @return Factory|View
     */
    public function edit(Request $request, $id = false){

        /* @var Trivia $trivia */
        $trivia = Trivia::where('id', $id)->first();
        if($trivia) {

            $data["trivia_id"] = $trivia->id;
			$data["quiz_title"]  = $trivia->quiz_title;
			$data["day"]  		= date('m/d/Y', strtotime($trivia->day));
			$data["image"]  	= $trivia->image;
            $data["mobile_image"]  	= $trivia->mobile_image;
            $data["question"]  	= $trivia->question;
            $data["answer1"]   	= $trivia->answer1;
            $data["answer2"]   	= $trivia->answer2;
            $data["answer3"]   	= $trivia->answer3;
            $data["answer4"]   	= $trivia->answer4;
        }

        $data['active_menu'] = 'trivia';
        return view('admin.trivia_form', $data);
    }


    /**
     * @param TriviaSaveRequest $request
     * @param int               $id
     *
     * @return RedirectResponse
     */
    public function save(TriviaSaveRequest $request, $id = false){

        /* @var Trivia $trivia */
        if($id)
        {
            $trivia = Trivia::where('id', $id)->first();
            $old_image = $trivia->image;
            $old_mobile_image = $trivia->mobile_image;
        }    
        else{
            $trivia = new Trivia();
        }

        $image = sanitizeStr($request->input("image"));
        $mobile_image = sanitizeStr($request->input("mobile_image"));

        //generate versions
        $image_temp_path = public_path('uploads/tmp') . DIRECTORY_SEPARATOR . $image;

        if(file_exists($image_temp_path))  {

            //move file to uploads
            moveFileToUploads($image, @$old_image, $id);

            $trivia->image 		= $image;
            
            //generate thumbnails
            $versions = [
                'mobile' => ['width' => 640, 'height' => 360 ],
            ];
            $image_parts = generate_image_versions($image, $versions);
            $trivia->mobile_image 	= $image_parts['mobile'];
            moveFileToUploads($trivia->mobile_image, @$old_mobile_image, $id);
        }

        $mobile_image_temp = public_path('uploads/tmp') . DIRECTORY_SEPARATOR . $mobile_image;
        if($mobile_image && file_exists($mobile_image_temp))  {

            moveFileToUploads($mobile_image, @$old_mobile_image, $id);
            $trivia->mobile_image 	= $mobile_image;
        }

        $trivia->day 		= sanitizeStr(date('Y-m-d', strtotime($request->input("day"))));
        $trivia->quiz_title = sanitizeStr($request->input("quiz_title"));
		$trivia->question 	= sanitizeStr($request->input("question"));
        $trivia->answer1  	= sanitizeStr($request->input("answer1"));
        $trivia->answer2  	= sanitizeStr($request->input("answer2"));
        $trivia->answer3  	= sanitizeStr($request->input("answer3"));
		$trivia->answer4  	= sanitizeStr($request->input("answer4"));

        if(!$trivia->code){
            $trivia->code = random_string(20);
        }
		
        $trivia->save();

        DataRevision::incrementRevision();
        Log::addFromRequest($request, shortClassMethod(__METHOD__));
        return redirect()->route("admin_trivias");
    }


    /**
     * @param Request $request
     *
     * @return RedirectResponse
     * @throws Exception
     */
    public function remove(Request $request){

        /* @var Trivia $trivia */
        $id = (int) sanitizeStr($request->input("record_id"));
        
        if($id > 0)  {
            $record_ids = $id;
        }
        else  {
            $record_ids = sanitizeStr($request->post('record_ids'));
        }
		
        //delete multiple rows
        if($record_ids)  {

            $recordIds = explode(",", $record_ids);

            $trivias = Trivia::whereIn('id', $recordIds)->get();
            
            foreach($trivias as $trivia)  {
                
                deleteUploadFile($trivia->image);
                deleteUploadFile($trivia->mobile_image);
                $trivia->delete();
            }

            DataRevision::incrementRevision();
            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".destroy", ["id" => $record_ids]);      
        }

        return '1';
    }
}
