<?php
namespace App\Http\Middleware;

use Closure;
use App\Framework\src\Http\Models\Admin;
use Illuminate\Http\Request;

class AdminCheck  {
    
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)  {

        if($request->session()->has('isLogined') && $request->session()->get('isLogined') === config('app.name'))  {

            $admin_id = $request->session()->get('backend_user_id');
            $admin    = Admin::selectRaw("*," . AESdecypt('email'))->where('id', $admin_id)->first();

            if($admin && $admin->user_type == "admin")
                return $next($request);
            else
                return redirect(route("admin_overview"));
        }
        return redirect('');
    }
}
