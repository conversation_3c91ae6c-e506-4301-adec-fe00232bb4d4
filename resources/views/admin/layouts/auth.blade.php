<!DOCTYPE html>
<html lang="zxx">

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="keyword" content="">
    <meta name="author" content="theme_ocean">
    <!--! The above 6 meta tags *must* come first in the head; any other head content must come *after* these tags !-->
    <!--! BEGIN: Apps Title-->
    <title>{{ config("app.name") }} - {{ $page_title }}</title>
    <!--! END:  Apps Title-->
    <!--! BEGIN: Favicon-->
    <link rel="shortcut icon" type="image/x-icon" href="{{ asset('public/assets') }}/images/favicon.ico">
    <!--! END: Favicon-->
    
    @yield('custom_css')
    
    <!--! BEGIN: Bootstrap CSS-->
    <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/css/bootstrap.min.css">
    <!--! END: Bootstrap CSS-->
    <!--! BEGIN: Vendors CSS-->
    <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/vendors.min.css">
    <!--! END: Vendors CSS-->
    <!--! BEGIN: Custom CSS-->
    <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/css/theme.min.css">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/css/custom.css">
    <!--! END: Custom CSS-->
    <!--! HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries !-->
    <!--! WARNING: Respond.js doesn"t work if you view the page via file: !-->
    <!--[if lt IE 9]>
			<script src="https:oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
			<script src="https:oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
		<![endif]-->

    <meta name="sitepath" content="{{ url('') }}">
    <meta name="csrf_token" content="{{ csrf_token() }}">
</head>

<body>
    <!--! ================================================================ !-->
    <!--! [Start] Main Content !-->
    <!--! ================================================================ !-->
    <main class="auth-cover-wrapper">
        <div class="auth-cover-content-inner">
            <div class="auth-cover-bg">
                <picture>
                    <source srcset="{{ asset('public/assets') }}/images/bg4.jpg" media="(min-width:576px)">
                    <source srcset="{{ asset('public/assets') }}/images/bg4.jpg" media="(min-width:1px)">
                    <img src="{{ asset('public/assets') }}/images/bg4.jpg" alt="Flowers">
                </picture>
            </div>
            <div class="auth-cover-content-wrapper">
                <div class="auth-img-wrap">
                    <div class="auth-img">
                        <img src="{{ asset('public/assets') }}/images/content-creator-logo-new.png" alt="Logo" class="img-fluid">
                    </div>
                    <h2 class="text-white m-0">Concepts and solutions</h2>
                </div>
            </div>
        </div>

        @yield('content')

    </main>
    <!--! ================================================================ !-->
    <!--! [End] Main Content !-->
    <!--! ================================================================ !-->
    
    <!--! ================================================================ !-->
    <!--! Footer Script !-->
    <!--! ================================================================ !-->
    
    <script>var hostUrl = "{{ asset('public/assets') }}/";</script>
    
    <!--! BEGIN: Vendors JS !-->
    <script src="{{ asset('public/assets') }}/vendors/js/vendors.min.js"></script>
    <!-- vendors.min.js {always must need to be top} -->
    <!--! END: Vendors JS !-->
    <!--! BEGIN: Apps Init  !-->
    <script src="{{ asset('public/assets') }}/js/common-init.min.js"></script>
    <!--! END: Apps Init !-->
    <!--! BEGIN: Theme Customizer  !-->
    <script src="{{ asset('public/assets') }}/js/theme-customizer-init.min.js"></script>
    <!--! END: Theme Customizer !-->

    <!--begin::Custom Javascript(used for this page only)-->
    @yield('custom_javascript')
    <!--end::Custom Javascript-->

</body>

</html>