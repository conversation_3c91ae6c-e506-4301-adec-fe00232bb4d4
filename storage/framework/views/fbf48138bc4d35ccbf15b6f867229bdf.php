<?php if(@$faqs): ?>
    <?php $__currentLoopData = $faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <!-- Start [ cc-accordion ] -->
        <div class="cc-accordion">
            <div class="cc-accordion-header">
                <button class="cc-accordion-handle cc-adobe-track" <?php echo adobeTrackData('accordion click', "faq detail", "1", "button"); ?> type="button" aria-expanded="<?php echo e(($key == 0) ? 'true' : 'false'); ?>">
                    <?php echo $faq['question']; ?>

                    <i class="cc_icon-plus-twex"><svg role="img"><title>Plus twex icon</title><use xlink:href="#cc_icon-plus-twex"></use></svg></i>
                    <i class="cc_icon-minus-twex"><svg role="img"><title>Minus twex icon</title><use xlink:href="#cc_icon-minus-twex"></use></svg></i>
                </button>
            </div>

            <div class="cc-accordion-body <?php echo e(($key == 0) ? 'show' : ''); ?>">
                <div class="cc-accordion-content">
                    <div class="text-a">
                        <?php echo auto_ptag($faq['answer']); ?>

                    </div>
                </div>
            </div>
        </div>
        <!-- End   [ cc-accordion ] -->
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?><?php /**PATH S:\contentcreators\svn\2018\clients\sied\dop-2025\resources\views/site/elements/faq_detail.blade.php ENDPATH**/ ?>