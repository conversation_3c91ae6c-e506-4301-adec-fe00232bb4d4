:root{
    scroll-behavior: auto;
}

:root {
    --cc-font-size: 14px;
}

:root {
    --text-1: 8px;
    --text-2: 10px;
    --text-3: 12px;
    --text-4: 14px;
    --text-5: 17px;
    --text-6: 20px;
    --text-7: 24px;
    --text-8: 29px;
    --text-9: 35px;
    --text-10: 42px;
    --text-11: 50px;
    --space-1: 2px;
    --space-2: 4px;
    --space-3: 7px;
    --space-4: 10px;
    --space-5: 14px;
    --space-6: 21px;
    --space-7: 28px;
    --space-8: 35px;
    --space-9: 42px;
    --space-10: 56px;
    --space-11: 70px;
    --space-12: 112px;
    --space-13: 224px;
    --color-1-light: 255 255 255;
    --color-1-dark: 18 19 20;
    --color-4-dark: 255 255 255;
    --color-10-dark: 83 177 255;
    --color-role-text-primary-base-dark: rgb(var(--color-4-dark)/1);
    --color-role-text-primary-base: var(--color-role-text-primary-base-dark);
    --color-role-page-backgrounds-primary: var(--color-role-page-backgrounds-primary-dark);
    --color-role-text-button-light: rgb(var(--color-1-light)/1);
    --color-role-page-backgrounds-primary-dark: rgb(var(--color-1-dark)/1);
    --color-role-text-link-base: var(--color-role-text-link-base-dark);
    --color-role-text-link-base-dark: rgb(var(--color-10-dark)/1);
    --icon-size-1: 12px;
    --icon-size-2: 18px;
    --icon-size-3: 28px;
    --icon-size-4: 40px;
    --icon-size-5: 64px;
    --icon-size-6: 96px;
    --sticker-size-1: 36px;
    --sticker-size-2: 42px;
    --sticker-size-3: 56px;
    --sticker-size-4: 70px;
    --sticker-size-5: 112px;
    --corner-size-0: 2px;
    --corner-size-1: 3px;
    --corner-size-2: 6px;
    --corner-size-3: 12px;
    --corner-size-4: 24px;
    --color-role-backgrounds-overlay-card-dark: rgb(var(--color-1-dark)/.8);

    --bs-form-valid-color: #00A088;
    --bs-form-valid-border-color: #00A088;
    --bs-success-rgb: 0, 160, 136;
    --bs-form-invalid-border-color: #FA3939;

}

html {
    /* this is static var ( same value in all themes ) */
    --psin-font: 'SST', Arial, sans-serif;
    --psin-login-bg-static: #1b507a;
    --psin-black-static: #000;
    --psin-black-15p-static: rgba(0, 0, 0, 0.15);
    --psin-black-30p-static: rgba(0, 0, 0, 0.3);
    --psin-black-50p-static: rgba(0, 0, 0, 0.5);
    --psin-black-60p-static: rgba(0, 0, 0, 0.6);
    --psin-black-85p-static: rgba(0, 0, 0, 0.85);
    --psin-black-100-static: #0b0b0b;
    --psin-black-200-static: #1f1f1f;
    --psin-black-200-70p-static: rgba(31, 31, 31, 0.7);
    --psin-black-200-80p-static: rgba(31, 31, 31, 0.8);
    --psin-black-200-90p-static: rgba(31, 31, 31, 0.9);
    --psin-black-300-static: #202020;
    --psin-black-400-0p-static: rgba(70, 70, 70, 0);
    --psin-black-700-static: #767676;
    --psin-white-static: #fff;
    --psin-gray-static: #f5f5f5;
    --psin-gray-20-80p-static: rgba(242, 242, 242, 0.8);
    --psin-gray-30-static: #f3f3f3;
    --psin-gray-100-static: #f0f0f0;
    --psin-gray-100-30p-static: rgba(240, 240, 240, 0.3);
    --psin-gray-100-40p-static: rgba(240, 240, 240, 0.4);
    --psin-gray-200-static: #eee;
    --psin-gray-250-static: #e5e5e5;
    --psin-gray-280-static: #e8e8e8;
    --psin-gray-300-static: #d1d1d1;
    --psin-gray-400-static: #dedede;
    --psin-gray-400-22p-static: rgba(222, 222, 222, 0.22);
    --psin-gray-500-static: #c4c4c4;
    --psin-blue-static: #2d64e6;
    --psin-blue-active-static: #123b9b;
    --psin-blue-100-static: #01bbef;
    --psin-blue-150-40p-static: rgba(1, 120, 191, 0.4);
    --psin-blue-200-static: #0074bc;
    --psin-blue-300-static: #525492;
    --psin-blue-400-static: #020669;
    --psin-blue-500-static: #0074BC;
    --psin-red-static: #e62d2d;
    --psin-red-active-static: #b31c1c;
    --psin-green-static: #8bb21e;
    --psin-green-100-static: #54e59b;
    --psin-orange-static: #ef7301;
    --psin-orange-100-static: #ed5f2b;
    --psin-orange-200-static: #d53b00;
    --psin-orange-200-active-static: #6e2108;

    /* for light theme (default) */
    --psin-login-bg: #1b507a;
    --psin-black: #000;
    --psin-black-15p: rgba(0, 0, 0, 0.15);
    --psin-black-30p: rgba(0, 0, 0, 0.3);
    --psin-black-50p: rgba(0, 0, 0, 0.5);
    --psin-black-60p: rgba(0, 0, 0, 0.6);
    --psin-black-85p: rgba(0, 0, 0, 0.85);
    --psin-black-100: #0b0b0b;
    --psin-black-200: #1f1f1f;
    --psin-black-200-70p: rgba(31, 31, 31, 0.7);
    --psin-black-200-80p: rgba(31, 31, 31, 0.8);
    --psin-black-200-90p: rgba(31, 31, 31, 0.9);
    --psin-black-300: #202020;
    --psin-black-400-0p: rgba(70, 70, 70, 0);
    --psin-black-700: #767676;
    --psin-white: #fff;
    --psin-gray: #f5f5f5;
    --psin-gray-20-80p: rgba(242, 242, 242, 0.8);
    --psin-gray-30: #f3f3f3;
    --psin-gray-100: #f0f0f0;
    --psin-gray-100-30p: rgba(240, 240, 240, 0.3);
    --psin-gray-100-40p: rgba(240, 240, 240, 0.4);
    --psin-gray-200: #eee;
    --psin-gray-250: #e5e5e5;
    --psin-gray-280: #e8e8e8;
    --psin-gray-300: #d1d1d1;
    --psin-gray-400: #dedede;
    --psin-gray-400-22p: rgba(222, 222, 222, 0.22);
    --psin-gray-500: #c4c4c4;
    --psin-blue: #2d64e6;
    --psin-blue-active: #123b9b;
    --psin-blue-100: #01bbef;
    --psin-blue-150-40p: rgba(1, 120, 191, 0.4);
    --psin-blue-200: #0074bc;
    --psin-blue-300: #525492;
    --psin-blue-400: #020669;
    --psin-blue-500: #0074BC;
    --psin-red: #e62d2d;
    --psin-red-active: #b31c1c;
    --psin-green: #8bb21e;
    --psin-green-100: #54e59b;
    --psin-orange: #ef7301;
    --psin-orange-100: #ed5f2b;
    --psin-orange-200: #d53b00;
    --psin-orange-200-active: #6e2108;

    --psin-body-bg: var(--psin-white-static);
    --psin-sidebar-bg: var(--psin-gray-200-static);
    --psin-widget-bg: var(--psin-black-300);
    --psin-widget-border-color: var(--psin-gray-400);
}

/* for dark theme */
html.prefers-color-mode-dark {
    --psin-login-bg: #1b507a;
    --psin-black: #fff;
    --psin-black-15p: rgba(255, 255, 255, 0.15);
    --psin-black-30p: rgba(255, 255, 255, 0.3);
    --psin-black-50p: rgba(255, 255, 255, 0.5);
    --psin-black-60p: rgba(255, 255, 255, 0.6);
    --psin-black-85p: rgba(255, 255, 255, 0.85);
    --psin-black-100: #fbfbfb;
    --psin-black-200: #f1f1f1;
    --psin-black-200-70p: rgba(31, 31, 31, 0.7);
    --psin-black-200-80p: rgba(31, 31, 31, 0.8);
    --psin-black-200-90p: rgba(31, 31, 31, 0.9);
    --psin-black-300: #202020;
    --psin-black-400-0p: rgba(70, 70, 70, 0);
    --psin-black-700: #c6c6c6;
    --psin-white: #000;
    --psin-gray: #050505;
    --psin-gray-20-80p: rgba(62, 62, 62, 0.8);
    --psin-gray-30: #030303;
    --psin-gray-100: #0f0f0f;
    --psin-gray-100-30p: rgba(60, 60, 60, 0.3);
    --psin-gray-100-40p: rgba(60, 60, 60, 0.4);
    --psin-gray-200: #111;
    --psin-gray-250: #151515;
    --psin-gray-280: #181818;
    --psin-gray-300: #4b4b4b;
    --psin-gray-400: #292929;
    --psin-gray-400-22p: rgba(55, 55, 55, 0.22);
    --psin-gray-500: #525252;
    --psin-blue: #2d64e6;
    --psin-blue-active: #123b9b;
    --psin-blue-100: #01bbef;
    --psin-blue-150-40p: rgba(1, 120, 191, 0.4);
    --psin-blue-200: #0074bc;
    --psin-blue-300: #525492;
    --psin-blue-400: #020669;
    --psin-blue-500: #0074BC;
    --psin-red: #e62d2d;
    --psin-red-active: #b31c1c;
    --psin-green: #8bb21e;
    --psin-green-100: #54e59b;
    --psin-orange: #ef7301;
    --psin-orange-100: #ed5f2b;
    --psin-orange-200: #d53b00;
    --psin-orange-200-active: #6e2108;

    --psin-body-bg: var(--psin-black-200-static);
    --psin-sidebar-bg: var(--psin-black-100-static);
    --psin-widget-bg: #181818;
    --psin-widget-border-color: rgba(60, 60, 60, 0.3);
}


.no-script-msg{font:0.750em Arial, verdana, Helvetica, sans-serif;background:#FFFFCC url(../gfx/icon-noScript.gif) no-repeat 5px 10px; width: auto; padding: 0.625em 0.625em 0.625em 1.5em; margin: 0.5em; border: .1rem solid #CD3D18; font-weight: bold; height: auto; font-size: 11px; color:var(--psin-black); line-height: 1.5em; clear: both; }
input[type="text"], input[type="email"], input[type="password"],input[type="number"], input[type="tel"], input[type="button"],input[type="reset"],input[type="submit"], button{-webkit-font-smoothing:antialiased;-moz-font-smoothing:antialiased; font-family: var(--psin-font); font-weight: 400; -webkit-appearance: none; appearance: none; }
html { font-size: 10px; }
body { min-width: 320px; font-size: 1.4rem; line-height: 1.5; overflow-x: hidden; }
html.overflow-yhidden body { overflow: hidden; }


html,
body {background-color: #fff; color: #1f1f1f; position: relative; font-family: var(--psin-font); font-weight: 400; }

html.footer-show,
html.footer-show body { overflow: hidden; }

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button { -webkit-appearance: none; margin: 0; }
input[type=number] { -webkit-appearance: none; appearance: none; margin: 0; }

a { color: var(--psin-blue); font-weight: normal; cursor: pointer; transition: color 0.15s linear; text-decoration: underline; outline: none; }
a:hover { color: var(--psin-blue); text-decoration: none; }
a.btn,
a.btn:hover { text-decoration: none; }
:focus-visible { outline: .2rem solid #fff; box-shadow: 0 0 0 .2rem #0b1f42; border-radius: .8rem; outline-offset: 0.2rem; }
:focus-visible:not(.btn):not(.swiper-pagination-bullet):not(.form-control):not(.cc-copy-icon) { border-radius: 0.8rem; }

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 { font-family: "sst",Arial,sans-serif; font-weight: 300; line-height: 1.25em; -webkit-margin-before: calc(var(--cc-font-size)*2); margin-block-start: calc(var(--cc-font-size)*2); -webkit-margin-after:  var(--cc-font-size); margin-block-end:  var(--cc-font-size); font-style: normal; }

h1,
.h1 { font-size: calc(var(--cc-font-size)*2.48832); }
h2,
.h2 { font-size: calc(var(--cc-font-size)*2.0736); }
h3,
.h3 { font-size: calc(var(--cc-font-size)*1.728); }
h4,
.h4 { font-size: calc(var(--cc-font-size)*1.44); }
h5,
.h5 { font-size: calc(var(--cc-font-size)*1.2); }
h6,
.h6 { font-size: var(--cc-font-size); }



blockquote,
.blockquote { -webkit-margin-after:  var(--cc-font-size); margin-block-end:  var(--cc-font-size); font-size: calc(var(--cc-font-size)*1.25); }

p { -webkit-margin-after:  var(--cc-font-size); margin-block-end:  var(--cc-font-size); }

.p1 {font-size: calc(var(--cc-font-size)*1.2); }
.p1 > :last-child,
.p1 > :last-child {margin-bottom: 0; }

ul, ol { display: block; margin: 0; -webkit-margin-after:  var(--cc-font-size); margin-block-end:  var(--cc-font-size); padding: 0; list-style: none; }
ul > li ,
ol > li { -webkit-padding-start: 2.4rem; padding-inline-start: 2.4rem; }

ul:first-child:not(.no-firstchild),
ol:first-child:not(.no-firstchild) { -webkit-margin-before: 0; margin-block-start: 0; }

ul:last-child:not(.no-lastchild),
ol:last-child:not(.no-lastchild) { -webkit-margin-after: 0; margin-block-end: 0; }

ul > li:last-child,
ol > li:last-child,
ul > li > :last-child,
ol > li > :last-child { -webkit-margin-after: 0; margin-block-end: 0; }

ul ul,
ul ol,
ol ol,
ol ul { margin: 0; }

li { position: relative; }

ul > li { display: block; list-style: none; }
ul > li::before { content: ""; display: block; position: absolute; top: 0.46em; left: 0.5em; width: 0.438em; height: 0.438em; background-color: #0068bd; border-radius: 0; }
.text-white ul > li::before,
ul.text-white > li::before { background-color: #fff; }
html[dir=rtl] ul > li::before { left: auto; right: -1.267em; }


ol { counter-reset: li; }
ol > li { display: block; list-style: none outside none; }
ol > li::before { content: counter(li, decimal)'.'; counter-increment: li; left: -0.5em; position: absolute; text-align: right; top: 0; width: 1.733em; color: #0068bd; font-weight: 400; }
.text-white ol > li::before,
ol.text-white > li::before { color: #fff; }
html[dir=rtl] ol > li::before { left: auto; right: -2.167em; text-align: left; }


dl { -webkit-margin-after: var(--cc-font-size); margin-block-end: var(--cc-font-size); }
dd { margin: 0; }

html select option { font-family: Arial, Helvetica, sans-serif; }




.mark,
mark { border-radius: 0.2rem; }


/*button*/
.btn-spacer{ font-size: 1em; line-height: normal;display: flex;flex-wrap: wrap; }
.btn { position: relative; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: var(--text-4); line-height: 1.25; cursor: pointer; padding: 0.7rem 1.4rem; border: transparent solid 0; border-radius: calc(var(--cc-font-size)*2); min-width: 1rem; text-align: center; transition: all .1s ease-in-out; margin: 0.4rem; text-transform: none; will-change: background-color, border-color, color; box-shadow: none !important; outline: 0 !important; }
.btn-icon { padding: 0.7rem; }
.btn-border { padding: 0.5rem 1.2rem; }
.btn-border.btn-icon { padding: 0.5rem; }

.btn i { font-size: 2rem; }
.btn-text { min-height: 2rem; }

.btn-border { border-width: 0.2rem; }

.btn-uppercase { text-transform: uppercase; }
.btn-uppercase .btn-text { top: 0.125em;  }

.btn i { margin: 0 0.6rem; line-height: 1; }
.btn-text { line-height: 1; display: flex; align-items: center; margin: 0 0.6rem; position: relative; }
.btn i + .btn-text,
.btn .btn-text + i { -webkit-margin-start: 0; margin-inline-start: 0; }

.btn > :first-child { -webkit-margin-start: 0; margin-inline-start: 0; }
.btn > :last-child { -webkit-margin-end: 0; margin-inline-end: 0; }


.btn:not(:disabled)::before,
.btn:not(.disabled)::before { outline: none; opacity: 0; border-radius: calc(var(--cc-font-size)*2); display: block; content: ''; position: absolute; top: -0.2rem; left: -0.2rem; right: -0.2rem; bottom: -0.2rem; will-change: box-shadow, opacity; transition: all .1s ease-in-out; }

.btn:disabled,
.btn.disabled { opacity: 0.5; pointer-events: none; opacity: 1; background-color: #d1d1d1; border-color: #d1d1d1; }

.btn.no-radius { border-radius: 0; }
.btn.no-radius:not(:disabled)::before,
.btn.no-radius:not(.disabled)::before { border-radius: 0; }


.btn:hover::before,
.btn:focus::before,
.btn:active::before { opacity: 1; }


.btn-primary { background-color: #0070CC; color: #fff; border-color: transparent; }
.btn-primary:hover { background-color: #0064b7; color: #fff; border-color: transparent; }
.btn-primary:focus { background-color: #0059a3; color: #fff; border-color: transparent; }
.btn-primary:active { background-color: #0059a3 !important; color: #fff !important; border-color: transparent !important; }

.btn-primary::before,
.btn-primary:hover::before { box-shadow: 0 0 0 0.2rem #0064b7; }
.btn-primary:focus::before { box-shadow: 0 0 0 0.2rem #0059a3; }
.btn-primary:active::before { box-shadow: 0 0 0 0.2rem #0059a3; }

.btn-primary:disabled,
.btn-primary.disabled { opacity: 0.5; background-color: #0070CC !important; border-color: #fff !important; }


.btn-white { background-color: #fff !important; color: #1f1f1f !important; border-color: transparent; }
.btn-white:hover { background-color: #e5e5e5 !important; color: #1f1f1f; border-color: transparent; }
.btn-white:focus { background-color: #ccc !important; color: #1f1f1f; border-color: transparent; }
.btn-white:active { background-color: #ccc !important; color: #1f1f1f; border-color: transparent; }

.btn-white::before,
.btn-white:hover::before { box-shadow: 0 0 0 0.2rem #e5e5e5; }
.btn-white:focus::before { box-shadow: 0 0 0 0.2rem #ccc; }
.btn-white:active::before { box-shadow: 0 0 0 0.2rem #ccc; }

.btn-white:disabled,
.btn-white.disabled { opacity: 1; background-color: #d1d1d1 !important; border-color: #d1d1d1; }

.btn-white-outline {color: #fff;border: 0.2rem solid; border-color: #fff; background-color: transparent; }
.btn-white-outline:not(:disabled):not(.disabled):hover,
.btn-white-outline:not(:disabled):not(.disabled):focus {color: #1F1F1F; background-color: #e5e5e5; border-color: transparent; }
.btn-white-outline:not(:disabled):not(.disabled):active { color: #1F1F1F; background-color: #ccc; border-color: transparent; }

.btn-white-outline:not(.disabled)::before { top: -0.4rem; left: -0.4rem; right: -0.4rem; bottom: -0.4rem; }
.btn-white-outline:not(:disabled):not(.disabled):hover::before { box-shadow: 0 0 0 0.2rem #fff; }
.btn-white-outline:not(:disabled):not(.disabled):focus::before { box-shadow: 0 0 0 0.2rem #ccc; }
.btn-white-outline:not(:disabled):not(.disabled):active::before { box-shadow: 0 0 0 0.2rem #ccc; }

.btn-white-outline.disabled, .btn-white-outline:disabled {color: #d1d1d1;border-color: #d1d1d1;background-color: transparent; }


.btn-primary-outline {color: #0070CC;border-color: #0070CC;background-color: transparent; }
.btn-primary-outline:not(:disabled):not(.disabled):hover {color: #fff; background-color: #0064b7; border-color: #0064b7; }
.btn-primary-outline:not(:disabled):not(.disabled):focus {color: #fff; background-color: #0059a3; border-color: #0059a3; }
.btn-primary-outline:not(:disabled):not(.disabled):hover::before {border-color: #0064b7;opacity: 1; }
.btn-primary-outline:not(:disabled):not(.disabled):focus::before {border-color: #0059a3;opacity: 1; }
.btn-primary-outline:not(:disabled):not(.disabled):active { color: #fff; background-color: #0059a3; border-color: #0059a3; }
.btn-primary-outline:not(:disabled):not(.disabled):active::before {border-color: #0059a3; }
.btn-primary-outline.disabled, .btn-primary-outline:disabled {color: #d1d1d1;border-color: #d1d1d1;background-color: transparent; }


.btn-secondary { background-color: #1f1f1f; color: #fff; border-color: transparent; }
.btn-secondary:focus { background-color: #1f1f1f; color: #fff; border-color: transparent; }
.btn-secondary:hover { background-color: #1f1f1f; color: #fff; border-color: transparent; }
.btn-secondary:active { background-color: #000 !important; color: #fff; border-color: transparent; }

.btn-secondary::before,
.btn-secondary:hover::before,
.btn-secondary:focus::before { box-shadow: 0 0 0 0.2rem #1f1f1f; }
.btn-secondary:active::before { box-shadow: 0 0 0 0.2rem #000; }

.btn-secondary:disabled,
.btn-secondary.disabled { opacity: 1; background-color: #d1d1d1 !important; border-color: #d1d1d1; }


.btn-secondary2 { background-color: #363636; color: #fff; border-color: transparent; }
.btn-secondary2:focus { background-color: #363636; color: #fff; border-color: transparent; }
.btn-secondary2:hover { background-color: #363636; color: #fff; border-color: transparent; }
.btn-secondary2:active { background-color: #262626 !important; color: #fff; border-color: transparent; }

.btn-secondary2::before,
.btn-secondary2:hover::before,
.btn-secondary2:focus::before { box-shadow: 0 0 0 0.2rem #363636; }
.btn-secondary2:active::before { box-shadow: 0 0 0 0.2rem #262626; }

.btn-secondary2:disabled,
.btn-secondary2.disabled { opacity: 1; background-color: #d1d1d1 !important; border-color: #d1d1d1; }

.btn-secondary-outline {color: #1f1f1f;border: 0.2rem solid;border-color: #1f1f1f;background-color: transparent;}
.btn-secondary-outline:not(:disabled):not(.disabled):hover,
.btn-secondary-outline:not(:disabled):not(.disabled):focus {color: #fff; background-color: #1f1f1f; border-color: #1f1f1f;}
.btn-secondary-outline:not(:disabled):not(.disabled):hover::before,
.btn-secondary-outline:not(:disabled):not(.disabled):focus::before {border-color: #1f1f1f;opacity: 1;}
.btn-secondary-outline:not(:disabled):not(.disabled):active { color: #fff; background-color: #000; border-color: #000; }
.btn-secondary-outline:not(:disabled):not(.disabled):active::before {border-color: #000;}

.btn-secondary-outline:not(.disabled)::before { top: -0.4rem; left: -0.4rem; right: -0.4rem; bottom: -0.4rem; }
.btn-secondary-outline:not(:disabled):not(.disabled):hover::before { box-shadow: 0 0 0 0.2rem #1f1f1f; }
.btn-secondary-outline:not(:disabled):not(.disabled):focus::before { box-shadow: 0 0 0 0.2rem #000; }
.btn-secondary-outline:not(:disabled):not(.disabled):active::before { box-shadow: 0 0 0 0.2rem #000; }

.btn-secondary-outline.disabled, .btn-secondary-outline:disabled {color: #d1d1d1;border-color: #d1d1d1;background-color: transparent;}


.btn-danger { background-color: #e62d2d; color: #fff; border-color: transparent; }
.btn-danger:focus { background-color: #e62d2d; color: #fff; border-color: transparent; }
.btn-danger:hover { background-color: #e62d2d; color: #fff; border-color: transparent; }
.btn-danger:active { background-color: #b31c1c; color: #fff; border-color: transparent; }

.btn-danger::before,
.btn-danger:hover::before,
.btn-danger:focus::before { box-shadow: 0 0 0 0.2rem #e62d2d; }
.btn-danger:active::before { box-shadow: 0 0 0 0.2rem #b31c1c; }

.btn-danger:disabled,
.btn-danger.disabled { opacity: 1; background-color: #d1d1d1; border-color: #d1d1d1; }


.btn-cta { background-color: #d53b00; color: #fff; border-color: transparent; }
.btn-cta:hover { background-color: #c03500; color: #fff; border-color: transparent; }
.btn-cta:focus { background-color: #aa2f00; color: #fff; border-color: transparent; }
.btn-cta:active { background-color: #aa2f00 !important; color: #fff !important; border-color: transparent !important; }

.btn-cta::before,
.btn-cta:hover::before { box-shadow: 0 0 0 2px #c03500; }
.btn-cta:focus::before { box-shadow: 0 0 0 2px #aa2f00; }
.btn-cta:active::before { box-shadow: 0 0 0 2px #aa2f00; }

.btn-cta:disabled,
.btn-cta.disabled { opacity: 0.5; background-color: #d1d1d1; border-color: #d1d1d1; }

.btn-black { background-color: #000; color: #fff; border-color: transparent; }
.btn-black:focus { background-color: #000; color: #fff; border-color: transparent; }
.btn-black:hover { background-color: #000; color: #fff; border-color: transparent; }
.btn-black:active { background-color: #000; color: #fff; border-color: transparent; }

.btn-black::before,
.btn-black:hover::before,
.btn-black:focus::before { box-shadow: 0 0 0 0.2rem #000; }
.btn-black:active::before { box-shadow: 0 0 0 0.2rem #000; }

.btn-black-outline {color: #1F1F1F;border: 0.2rem solid; border-color: #1F1F1F; background-color: transparent; }
.btn-black-outline:not(:disabled):not(.disabled):hover,
.btn-black-outline:not(:disabled):not(.disabled):focus {color: #fff; background-color: #1F1F1F; border-color: transparent; }
.btn-black-outline:not(:disabled):not(.disabled):active { color: #fff; background-color: #000; border-color: transparent; }

.btn-black-outline:not(.disabled)::before { top: -0.4rem; left: -0.4rem; right: -0.4rem; bottom: -0.4rem; }
.btn-black-outline:not(:disabled):not(.disabled):hover::before { box-shadow: 0 0 0 0.2rem #1F1F1F; }
.btn-black-outline:not(:disabled):not(.disabled):focus::before { box-shadow: 0 0 0 0.2rem #1F1F1F; }
.btn-black-outline:not(:disabled):not(.disabled):active::before { box-shadow: 0 0 0 0.2rem #000; }

.btn-black-outline.disabled, .btn-black-outline:disabled {color: #d1d1d1;border-color: #d1d1d1;background-color: transparent; }

.btn-twitch { background-color: #7c3cdd; color: #fff; border-color: transparent; }
.btn-twitch:hover { background-color: #9556f3; color: #fff; border-color: transparent; }
.btn-twitch:focus { background-color: #752edd; color: #fff; border-color: transparent; }
.btn-twitch:active { background-color: #752edd !important; color: #fff !important; border-color: transparent !important; }

.btn-twitch::before,
.btn-twitch:hover::before { box-shadow: 0 0 0 0.2rem #9556f3; }
.btn-twitch:focus::before { box-shadow: 0 0 0 0.2rem #752edd; }
.btn-twitch:active::before { box-shadow: 0 0 0 0.2rem #752edd; }

.btn-twitch:disabled,
.btn-twitch.disabled { opacity: 0.5; background-color: #7c3cdd !important; border-color: #fff !important; }



[class*="cc_icon-"] { font-size: 1em; width: 1em; max-width: 1em; flex: 0 0 1em; display: block; position: relative; }
[class*="cc_icon-"]::before { content: ""; display: block; padding-top: 100%; position: relative; width: 100%; z-index: 1; }
[class*="cc_icon-"] svg,
[class*="cc_icon-"] img { display: block; width: 100%; height: 100%; position: absolute; top: 0; left: 0; z-index: 0; }

.skip-content { margin: 0; position: absolute; top: 40px; left: 20px; z-index: 1000; transform: translateY(-200%); transition: transform 0.2s ease-in-out; }
.skip-content:focus { transform: translateY(0); }


.layout { position: relative; z-index: 1; }
.container { padding-left: 1.5rem; padding-right: 1.5rem; max-width: 540px; }
.row { --bs-gutter-x: 3rem }

.cc-section { position: relative; padding: 4.4rem 0; z-index: 0; width: 100%; }
.cc-section-inner { position: relative; z-index: 2; width: 100%; flex: 0 0 100%; max-width: 100%; }
.cc-section-bg { position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 0; display: block; }
.cc-section-bg picture,
.cc-section-bg iframe,
.cc-section-bg video,
.cc-section-bg img { display: block; width: 100%; height: 100%; -o-object-fit: cover; object-fit: cover; }

.ratio img,
.ratio video,
.ratio iframe { display: block; width: 100%; height: 100%; top: 0; left: 0; position: absolute; z-index: 1; -o-object-fit: cover; object-fit: cover; }
.ratio.contain img,
.ratio.contain video {-o-object-fit: contain;object-fit: contain;}
.ratio.static { width: 100%; position: relative; display: flex; flex-direction: column; align-items: center; justify-content: center; }
.ratio.static::before { display: none; }
.ratio.static img { position: static; width: auto; height: auto; max-width: 100%; max-height: 100%; }


/* splash-screen */
.splash-screen { display: none; position: fixed; top: 0; right: 0; bottom: 0; left: 0; z-index: 111111; align-items: center;justify-content: center; background-color: #ffffff; }
.splash-screen-content{padding: 15px; display: flex; justify-content: center;align-items: center;flex-direction: column; text-align: center;position: relative; }
.splach-screen-icon{font-size: 6.6rem; margin-bottom: 1.9rem; }

@media screen and (min-width: 568px) and (orientation:landscape) {
   .splash-screen{display: flex; }
   html,body { width: 100%; height: 100%; overflow: hidden; position: relative; }
}

/* cc-pages animation */
.cc-pages { position: relative; z-index: 0; width: 100%; overflow: hidden; background-color: #fff; }
.cc-page { display: none; opacity: 1; position: relative; top: 0; left: 0; z-index: 0; background-color: #fff; width: 100%; box-shadow: 0 0 8px 0 rgb(0, 0, 0, 75%); }
.cc-page.active { display: block; z-index: 1; opacity: 1; }

.cc-pages > .cc-page.next-will,
.cc-pages > .cc-page.back-will { display: block; z-index: 2; transition-duration: 400ms; transition-property: transform, opacity; overflow-y: hidden; position: fixed; }

.cc-pages > .cc-page.next-will { transform: translateX(100%); position: fixed; }

.cc-pages > .cc-page.back-will { transform: translateX(-100%); position: fixed; }

.cc-pages > .cc-page.active-will { transform: translateX(0); opacity: 1; }

.cc-pages > .cc-page.next-was,
.cc-pages > .cc-page.back-was { transform: translateX(0); transition-duration: 300ms; transition-property: transform, opacity; }

.cc-pages > .cc-page.next-was.active-was { transform: translateX(-20%); opacity: 1; }

.cc-pages > .cc-page.back-was.active-was { transform: translateX(20%); opacity: 1; }


.cc-dashboard-header { position: fixed; top: 0; left: 0; width: 100%; min-width: 32rem; z-index: 2; height: 4.4rem; padding: 0; color: #ffffff; background-color: #1F1F1F; }
.cc-dashboard-footer {background-color: #1F1F1F;  position: fixed; bottom: 0; left: 0; width: 100%; min-width: 32rem; z-index: 2; height: 3.4rem;
color: #fff; display: block; }
html.detail-page .gdk { display: none; }
.cc-page-inner { padding: 4.4rem 0 0 0; position: relative; z-index: 0; width: 100%; min-width: 32rem; max-width: 54rem; display: flex; margin: 0 auto; }
.cc-page-inner::before { display: block; content: ''; height: calc(100vh - 4.4rem); }
html.detail-page .cc-page-inner { padding-bottom: 0; }
.cc-page-content { position: relative; z-index: 1; width: 100%; min-width: 32rem; max-width: 54rem; }
.cc-dashboard .cc-page-content { display: flex; }
.cc-dashboard .db-body-content { width: 100%; max-width: 100%; flex: 0 0 100%; }

.db-body-content { height: 100%; padding: 0 2rem;display: flex;flex-direction: column; }
.db-body-component { padding-bottom: 2rem; }
.db-body-bottom { margin-top: auto;padding: 0 0 2rem 0;position: relative;z-index: 1; }

/* text-a */
.text-a { display: flex; flex-direction: column; justify-content: center; color: var(--psin-black-200-static); }
.text-a.text-center { align-items: center; margin-left: auto; margin-right: auto; }
.text-a > * { margin-bottom: 1.4rem; }
.text-a > .btn-wrapper { margin-top: 1.4rem; }
.text-a > :first-child { margin-top: 0; }
.text-a > :last-child { margin-bottom: 0; }


.cc-tabs-nav-wrap { padding: 2rem 1.2rem 0 1.2rem; margin: 0 -2rem 1.5rem -2rem; overflow: hidden; overflow-x: auto; flex-wrap: nowrap; white-space: nowrap; -webkit-overflow-scrolling: touch; border-bottom: 0.1rem solid var(--bs-white); }
.cc-tabs-nav-wrap::-webkit-scrollbar { height: 0; }
.cc-tabs-nav-wrap::-webkit-scrollbar-track { background-color: transparent; border-radius: 0; }
.cc-tabs-nav-wrap::-webkit-scrollbar-thumb { background-color: transparent; border-radius: 0; }
.cc-tabs-nav-wrap::-webkit-scrollbar-thumb:hover { background-color: transparent; }
.cc-tabs-nav { padding: 0; margin: 0; list-style: none; display: flex; align-items: center; flex-wrap: nowrap; }
.cc-tabs-nav > li { padding: 0; margin: 0; list-style: none; display: flex; align-items: center; position: static; }
.cc-tabs-nav > li::before { display: none; }
.cc-tabs-nav > li > a,
.cc-tabs-nav > li > button { padding: 0.8rem; margin: 0; color: #fff; font-size: 1.4rem;font-style: normal;font-weight: 700; line-height: 1.4; text-decoration: none; position: relative; display:flex; align-items: center; }
.cc-tabs-nav > li > a::before,
.cc-tabs-nav > li > button::before { opacity: 0; visibility: hidden; bottom: 0; left: 0; right: 0; height: 0.2rem; background-color: #0070CC; position: absolute; content: ''; display:block; }
.cc-tabs-nav > li > a.dt-nav-active,
.cc-tabs-nav > li > button.dt-nav-active { color: #0070CC; }
.cc-tabs-nav > li > a.active::before,
.cc-tabs-nav > li > button.active::before { opacity: 1; visibility: visible; }
.cc-tabs-nav > li > a.dt-nav-active::before,
.cc-tabs-nav > li > button.dt-nav-active::before { opacity: 1; visibility: visible; }

.cc-quiz-question { margin-top: 0; --bs-heading-color: #fff; font-weight: 300; }

.cc-quiz-toggle-a { display: flex; align-items: center; justify-content: flex-start; position: relative;font-weight: 400;cursor: pointer; color: #1F1F1F; font-size: 0.889rem; line-height: 1.438; font-weight: 300; margin: 0 0 0.7rem 0; }
.cc-quiz-toggle-a input { width: 0.1rem;height: 0.1rem;position: absolute;top: 0;left: 0;opacity: 0 !important; padding: 0; border: 0; display: block; }
.cc-quiz-toggle-a-ui { width: 100%; max-width: 100%; flex: 0 0 100%; cursor: pointer; display: flex; align-items: center; margin: 0; position: relative; background: #ffffff; border-radius: 1.5rem; padding: 1.4rem; }
.cc-quiz-toggle-a:not(:hover) input:focus ~ .cc-quiz-toggle-a-ui { outline: .2rem solid #fff; box-shadow: 0 0 0 .2rem #0b1f42; border-radius: .8rem; outline-offset: 0.2rem; }
.cc-quiz-toggle-a-text { -webkit-user-select: none;user-select: none; display: block; position: relative; top: 0.125em;}
.cc-quiz-question-lcell { margin-right: 1.5rem; width: 1.6rem; font-size: 1.5rem; line-height: normal; font-weight: 400; color: #1F1F1F;display: flex; align-items: center; justify-content:center; }
.cc-quiz-question-text { margin: 0; font-size: 1.4rem; line-height: 1.5; font-weight: 400; color: #1F1F1F;display: block; }
.form-group.has-error .cc-quiz-toggle-a-ui { border-color: #dc3545; }

.cc-quiz-toggle-a input:checked ~ .cc-quiz-toggle-a-ui { background: #0070CC; }
.cc-quiz-toggle-a input:checked ~ .cc-quiz-toggle-a-ui .cc-quiz-question-lcell { color: #fff; }
.cc-quiz-toggle-a input:checked ~ .cc-quiz-toggle-a-ui .cc-quiz-question-text { color: #fff; }
.cc-quiz-toggle-a input:disabled ~ .cc-quiz-toggle-a-ui { border-color: rgba(217, 217, 217, 0.35); }
.cc-quiz-toggle-a input:disabled ~ .cc-quiz-toggle-a-ui .cc-quiz-question-lcell { color: rgba(31, 31, 31, 0.35); }
.cc-quiz-toggle-a input:disabled ~ .cc-quiz-toggle-a-ui .cc-quiz-question-text { color: rgba(31, 31, 31, 0.35); }
.cc-quiz-toggle-a[data-answer=correct] input ~ .cc-quiz-toggle-a-ui { background: #00A088; }
.cc-quiz-toggle-a[data-answer=correct] input ~ .cc-quiz-toggle-a-ui .cc-quiz-question-lcell { color: #fff; }
.cc-quiz-toggle-a[data-answer=correct] input ~ .cc-quiz-toggle-a-ui .cc-quiz-question-text { color: #fff; }
.cc-quiz-toggle-a[data-answer=wrong] input ~ .cc-quiz-toggle-a-ui { background: #FA3939; }
.cc-quiz-toggle-a[data-answer=wrong] input ~ .cc-quiz-toggle-a-ui .cc-quiz-question-lcell { color: #fff; }
.cc-quiz-toggle-a[data-answer=wrong] input ~ .cc-quiz-toggle-a-ui .cc-quiz-question-text { color: #fff; }


@media (max-width: 374px) {
    .cc-quiz-toggle-a-ui { padding: 1.4rem 1.2rem; }
    .cc-quiz-question-lcell { margin-right: 1rem; }
    .cc-quiz-question-text { font-size: 1.2rem; }
}

/* memory game */
.cc_mg_game-group { margin-bottom: 1.4rem; }
.cc_mg_game-wrap { margin: 0 -5px; position: relative; }
.cc_mg_game { position: relative; }
.cc_mg-game-stage { width: 100%; max-width: 100%; display: flex; flex-wrap: wrap; position: relative; z-index: 0; transition: opacity 0.3s ease; }
.cc_mg-game-stage.done { opacity: 0.5; }
.cc_mg-game-card-holder { display: block; width: calc(100% / 6); max-width: calc(100% / 6); flex: 0 0 calc(100% / 6); padding: 0.3rem; transform: scale(.75); opacity: 0; visibility: hidden; transition: all 0.4s cubic-bezier(.17,.89,.31,1.29); }
.cc_mg-game-card-holder.show.done { opacity: 0.5; }

.cc_mg_game[data-grid=a4] .cc_mg-game-card-holder { width: 25%; max-width: 25%; flex: 0 0 25%; }
.cc_mg_game[data-grid=a5] .cc_mg-game-card-holder { width: 20%; max-width: 20%; flex: 0 0 20%; }

.cc_mg-game-card-holder.show { opacity: 1; visibility: visible; transform: scale(1); }
.cc_mg-game-card-container { display: block; width: 100%; position: relative; perspective: 200px; transform-style: preserve-3d; }
.cc_mg-game-card { box-shadow: none !important; display: block; position: relative; width: 100%; padding: 0; margin: 0; outline: 0 !important; border-radius: 4px; transform-style: preserve-3d; transition: opacity 1s cubic-bezier(0.175, 0.885, 0.32, 1.275), transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275); background: none !important; border: 0 !important; background-color: transparent;}
.cc_mg-game-card::before { display: block; width: 100%; content: ''; padding: 100% 0 0 0; }
.cc_mg-game-card::after { display: none; width: 100%; height: 100%; content: ''; top: 0; left: 0; position:absolute; border-radius: 1.5rem; background-color:#D9D9D9; }
.cc_mg-game-card-front { display: block; position: absolute; top: 0; left: 0; z-index: 0; width: 100%; height: 100%; z-index: 1; transform-style: preserve-3d; /*-webkit-transition: -webkit-transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275); transition: -webkit-transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275); -o-transition: transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275); transition: transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275); transition: transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275), -webkit-transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275); -webkit-transform: rotateY(0deg);*/ transform: rotateY(0deg); border-radius: 5px; backface-visibility: hidden; }
.cc_mg-game-card-front img { display: block; position: absolute; top: 0; left: 0; z-index: 0; width: 100%; height: 100%; border-radius: 1.5rem; }
.cc_mg-game-card-back { display: block; position: absolute; top: 0; left: 0; z-index: 11; width: 100%; height: 100%; transform-style: preserve-3d; /* -webkit-transition: -webkit-transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275); transition: -webkit-transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275); -o-transition: transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275); transition: transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275); transition: transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275), -webkit-transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);*/ transform: rotateY(-180deg); border-radius: 1.5rem; backface-visibility: hidden; background-repeat: no-repeat; background-position: center center; background-size: contain; overflow: hidden;border: 0 !important; outline: none !important; overflow: hidden; box-shadow: 0px 2px 10px rgba(31, 31, 31, 0.4); }
.cc_mg-game-card-back img { display: block; position: absolute; top: 0; left: 0; z-index: 0; width: 100%; height: 100%; border-radius: 5px; }
.cc_mg-game-card-back::before{display: block;position: absolute;top: 0;left: 0;right: 0; bottom: 0; border-radius: 5px; border: 1px solid rgba(151, 151, 151, 0.5); content: '';pointer-events: none;z-index: 1;}
.cc_mg-game-card.active .cc_mg-game-card-front,
.cc_mg-game-card.done .cc_mg-game-card-front { transform: rotateY(180deg); }
.cc_mg-game-card.active .cc_mg-game-card-back,
.cc_mg-game-card.done .cc_mg-game-card-back { transform: rotateY(0deg); }
.cc_mg-game-card.active { cursor: default; }
/*.cc_mg-game-card.done { cursor: default; opacity: 0; pointer-events: none; visibility: hidden; }*/
.cc_mg-game-card.done { cursor: default; pointer-events: none; }
.cc_mg-game-card.done::after { display: block; }
.cc_mg-game-card.done .cc_mg-game-card-back { box-shadow:none; }

.cc_mg-game-card .cc_mg-game-card-front::after { opacity: 0; border: 1px solid #0070cc; border-radius: 1.5rem; top: 0; right: 0; bottom: 0; left: 0; position: absolute; z-index: 1; content: ''; display: block; pointer-events: none; transition: all 0.15s linear; }
.cc_mg-game-card .cc_mg-game-card-back::after { opacity: 0; border: 2px solid #0070cc; border-radius: 1.5rem; top: 0; right: 0; bottom: 0; left: 0; position: absolute; z-index: 1; content: ''; display: block; pointer-events: none; transition: all 0.15s linear; }

.cc_mg-game-card:not(:hover):focus .cc_mg-game-card-front::after,
.cc_mg-game-card:not(:hover):focus .cc_mg-game-card-back::after { opacity: 1; }

.cc_mg_game-status { width: 100%; height: 100%; position: absolute; top:0; left:0; z-index:11; display: flex; align-items: center; justify-content: center; pointer-events: none; }
.cc_mg_game-done-icon { width: 8rem; height: 8rem; font-size: 8rem; background-color: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 11; }
.cc_mg_game-done-icon .cc_icon-check-2 { color: #0070CC; }
.cc_mg_game-done-icon .cc_icon-close-2 { color: #FA3939; }

.cc_mg-game-card-val { padding: 6px 5px 4px 5px; bottom: 5px; right: 5px; font-size: 8px; line-height: normal; font-weight: 400; color: #fff; position: absolute; z-index: 11; background: linear-gradient(0deg, rgba(0, 0, 0, .2) 0%, rgba(0, 0, 0, .2) 100%), linear-gradient(152deg, #935dfb 0%, #4c76fe 50%, #00b0fb 100%); box-shadow: rgba(0, 0, 0, 0.1) 0px 0.5rem 0.5rem 0px; border-radius: 5px; display: block; -webkit-border-radius: 5px; -moz-border-radius: 5px; -ms-border-radius: 5px; -o-border-radius: 5px; }

@media (max-width: 374px) {
    .cc_mg_game-group { margin-bottom: 1rem; }
}

/* loading */
.cc-loading { width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 11;background-color: rgb(31, 31, 31, 94%);min-width: 320px;visibility: hidden;opacity: 0;transition: visibility 0.25s, opacity 0.25s; }
.loading .cc-loading { visibility: visible;opacity: 1; z-index: 99999999; }
.cc-loading-inner { width: 70%;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);text-align: center; }
.cc-loading-inner p { font-size: 1.4rem;font-weight: 400; color: #fff; }
.cc-loading-inner svg { width: 6rem;height: 6rem;display: block;margin: 0 auto 3.2rem auto; }
.cc-loading-inner .icon-primary-color { fill: #fff; }
.cc-loading-inner .icon--part-1 {animation-iteration-count:infinite;animation-duration:2000ms;animation-delay:400ms;animation-name:logo-mark-1; }
.cc-loading-inner .icon--part-2 {animation-iteration-count:infinite;animation-duration:2000ms;animation-delay:800ms;animation-name:logo-mark-2; }
.cc-loading-inner .icon--part-3 {animation-iteration-count:infinite;animation-duration:2000ms;animation-delay:1200ms;animation-name:logo-mark-3; }
.cc-loading-inner .icon--part-4 {animation-iteration-count:infinite;animation-duration:2000ms;animation-delay:1600ms;animation-name:logo-mark-4; }
@keyframes logo-mark-1{0%{fill:#fff}40%{fill:#3e3e3e}60%{fill:#3e3e3e}100%{fill:#fff}}
@keyframes logo-mark-2{0%{fill:#fff}40%{fill:#3e3e3e}60%{fill:#3e3e3e}100%{fill:#fff}}
@keyframes logo-mark-3{0%{fill:#fff}40%{fill:#3e3e3e}60%{fill:#3e3e3e}100%{fill:#fff}}
@keyframes logo-mark-4{0%{fill:#fff}40%{fill:#3e3e3e}60%{fill:#3e3e3e}100%{fill:#fff}}

/* mob other.css */
.form-control::placeholder { --bs-secondary-color: rgba(0, 0, 0, 0.4); font-weight: 300; }

.form-group { margin-bottom: 0.7rem; position: relative; }
.form-label { display: block; color: #1F1F1F; font-size: 1.6rem; line-height: 1.313; margin-bottom: 0.5rem; }
.form-control-wrapper { position: relative; }
.form-control { --bs-body-bg: #fff; --bs-body-color: #000; --bs-border-radius: 0.5rem; --bs-border-color: #D9D9D9; font-size: 1.5rem; height: 4.2rem; padding: 1.2rem 1.4rem; font-weight: 400; }
.form-control:focus { border-color: #D9D9D9; box-shadow: none; }
.invalid-feedback { text-align: center; margin-bottom: 0; color: #fff; font-size: 1.4rem; line-height: 1.5; padding: 0.8rem; background-color: #FA3939; border-radius: 0.3rem; display: none; margin-top: 0.5rem; }
.form-control.is-valid ~ .invalid-feedback,
.was-validated .form-control:valid ~ .invalid-feedback { background-color: #00A088; display: block; }
.form-control.border-primary { --bs-border-color: #0070CC; }


.was-validated .form-control:valid ~ .cc-toggle-a-ui,
.form-control.is-valid ~ .cc-toggle-a-ui { border-color: #00A088; }

.form-control.is-invalid ~ .cc-toggle-a-ui,
.was-validated .form-control:invalid ~ .cc-toggle-a-ui { border-color: #FA3939;  }

.error-no-spacing { position: absolute; left: 0; }
.form-control:not(:hover):focus { outline: .2rem solid #fff; box-shadow: 0 0 0 .2rem #0b1f42; border-radius: 0.5rem; outline-offset: 0.2rem; }

.was-validated .form-control:valid:focus { box-shadow: 0 0 0 0.1rem rgba(var(--bs-success-rgb), 1); }
.form-control.is-invalid:focus,
.was-validated .form-control:invalid:focus { box-shadow: 0 0 0 0.1rem rgba(var(--bs-danger-rgb), 1); }

.cc-toggle-a .form-control { width: 0; height: 0; opacity: 0; position: absolute; top: 0; left: 0; padding: 0; }
.cc-toggle-a { display: flex; position: relative; }
.cc-toggle-a-ui { border-radius: 0.2rem; border: 1px solid #F5F7FA; background: #fff; display: flex; align-items: center; justify-content: center; width: 1.8rem; height: 1.8rem; flex-shrink: 0; margin-right: 0.6rem; color: #0070CC; }

.cc-toggle-a-text { width: 1%; flex: 1 1 auto; -webkit-user-select: none; user-select: none; font-size: 1.2rem; font-weight: 400; color: #6B6B6B; }
.cc-toggle-a-text a { font-weight: 500; }
.cc-toggle-a-ui i { opacity: 0; transition: 0.3s ease-in-out; }
.cc-toggle-a input:checked ~ .cc-toggle-a-ui i { opacity: 1; }
.cc-toggle-a .form-control:not(:hover):focus ~ .cc-toggle-a-ui { outline: .2rem solid #fff; box-shadow: 0 0 0 .2rem #0b1f42; border-radius: .8rem; outline-offset: 0.2rem; }

.cc-tab-a { --bs-border-radius: 0;--bs-nav-pills-link-active-bg: #CAE0F2;flex-direction: column; }
.cc-tab-a > li::before { display: none; }
.cc-tab-a > li:last-child .nav-link { margin-bottom: 0 !important; }

.cc-bt-description { border-radius: 1.5rem; background: rgba(0, 0, 0, 0.15);box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.10); -webkit-backdrop-filter: blur(2.5px); backdrop-filter: blur(2.5px); padding: 1rem 1.6rem; color: #fff; text-align: center; }
.cc-game-thumb { --bs-aspect-ratio: 56.25%; border-radius: 1.5rem; overflow: hidden; margin-bottom: 1.4rem; box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.10); }
.cc-game-thumb.math-riddle { background-color: #fff; }
.cc-game-thumb::after { display: block; content: ''; background-color: #000; opacity: 0; z-index: 1; position: absolute; top: 0; left: 0; width: 100%; height: 100%; }
.cc-game-thumb.done::after { opacity: 0.5; }
.cc-game-thumb .cc_mg_game-done-icon { width: 6rem; height: 6rem; font-size: 6rem; }
.cc-magic-spot-row  { display: flex; align-items: center; color: #fff; margin-bottom: 0.7rem;  }
.cc-magic-spot-eype { font-size: 3.4rem; color: #fff; display: block; margin-right: 0.7rem; }
.cc-magic-spot-eypetext { font-weight: 300; margin: 0; }

.cc-magic-redeem-form-row { display: flex; }
.cc-magic-redeem-form-lcell { flex: 1 1 auto; width: 1%; }
.cc-magic-redeem-form-rcell { margin-left: 0.8rem; flex-shrink: 0; }
.cc-magic-redeem-form-lcell .invalid-feedback { width: calc(100vw - 4rem); }

.cc-find-the-mistakes-wrap { position: relative; }
.cc-find-the-mistakes-img { transition: opacity 0.3s ease; }
.cc-find-the-mistakes-img.done { opacity: 0.5; }
.cc-find-the-mistakes-heading { color: #fff; margin-bottom: 1.4rem; text-align: center; }
.cc-find-the-mistakes-heading .h3 { font-weight: 300; }
.cc-find-the-mistakes-thumb { --bs-aspect-ratio: 56.25%; border-radius: 1.5rem; overflow: hidden; margin-bottom: 0.7rem; }
.cc-game-ans { width: 4rem; height: 4rem; font-size: 4rem; border-radius: 50%; background-color: #fff; display: flex; align-items: center; justify-content: center; transform: translate(-50%, -50%); position: absolute; pointer-events: none; z-index: 10; }
.cc-game-ans .cc_icon-check-2 { color: #0070CC; }
.cc-game-ans .cc_icon-close { color: #FF6E6E; font-size: 2.4rem; }
.cc-participate-wrap { padding-top: 2rem; }

/* .cc-pd-tagname { display: flex; justify-content: center; margin-bottom: 1.4rem; }
.cc-pd-tagname-box { padding: 0.7rem 1.5rem 0.4rem 1.5rem; display: flex; position: relative; }
.cc-pd-tagname-box::before { content: ''; width: 100%; height: 100%; position: absolute; top: 0; left: 0; background-color: #0070CC; z-index: 1; transform: skewX(-15deg); -webkit-transform: skewX(-15deg); -moz-transform: skewX(-15deg); -ms-transform: skewX(-15deg); -o-transform: skewX(-15deg); }
.cc-pd-tagname-box-in { padding: 0; font-size: 1.7rem; line-height: 1.5; font-weight: 300; color: #FFF; display: flex; align-items:center; position: relative; z-index: 11; } */
.cc-participate-thumb { max-width: 17.2rem; margin: 0 auto 1.4rem auto; }
.cc-participate-content { --bs-heading-color: #0070CC; color: #6B6B6B; text-align: center; margin-bottom: 2rem; }

.cc-tc-row { display: flex; align-items: flex-start; margin-bottom: 0.7rem; }
.cc-tc-left { flex: 1; width: 1%; }
.cc-tc-right { display: flex; align-items: center; flex-shrink: 0; }
.cc-tc-icon { width: 4.2rem; display: block; margin: 0 1.6rem; }

@media (max-width: 359px) {
    .cc-tc-row { flex-direction: column; }
    .cc-tc-left { width: 100%; flex: auto; }
    .cc-tc-right { padding-top: 1rem; width: 100%; }
}


/* input label focus animation */
.cc-ticket-val-row .form-label { font-size: 1.2rem; top: 0.3rem; color: #6B6B6B; }
.form-control.form-control-label { padding: 1.6rem 1rem 0.9rem 1rem; font-weight: 300 !important; }
.form-control.form-control-label + .form-label { margin-bottom: 0; padding: 1.1rem 1rem; }
.form-control-wrapper .form-label { position: absolute; top: 0.1rem; left: 0.1rem; pointer-events: none; transition: all 0.3s; }
.form-control.label-animation-autofill ~ .form-label,
.form-control.label-animation ~ .form-label { font-size: 0.8rem; top: 0.4rem; padding-top: 0; padding-left: 0.7rem; padding-bottom: 0; }

@keyframes onAutoFillStart {
    from { } to { }
}

@keyframes onAutoFillCancel {
    from { } to { }
}

input:-webkit-autofill { animation-name: onAutoFillStart; -webkit-transition: background-color 50000s ease-in-out 0s, color 50000s ease-in-out 0s, border-color 50000s ease-in-out 0s; transition: background-color 50000s ease-in-out 0s, color 50000s ease-in-out 0s, border-color 50000s ease-in-out 0s; }
input:not(:-webkit-autofill) { animation-name: onAutoFillCancel; }

.cc-avtar-thumb { border-radius: 3rem; overflow: hidden; max-width: 21.2rem; margin: 0 auto 1.4rem auto; box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.16); }

.cc-cl-form-row { margin-bottom: 2.4rem; display: flex; flex-direction: row; align-items: flex-start; position: relative; flex-wrap: wrap; }
.cc-cl-form-left { padding-right: 1rem; flex: 1; width:1%; }
.cc-cl-form-left .form-control { font-size: 1.2rem; color: #6B6B6B; }
.cc-cl-form-left .form-label { color: #6B6B6B; }
.cc-cl-form-row .cc-copy-icon { width: 4.2rem; height: 4.2rem; font-size: 2rem; color: #0070CC; background: none; }
.cc-cl-form-row .cc-code-copied-lbl { margin: 0.5rem 0 1rem 0; padding: 0.5rem 1rem; min-height: 2.3rem; font-size: 1rem;font-style: normal;line-height: normal; color: #00A088; text-align: center; width: 100%; position: static; border-radius: 0.3rem;background: rgba(0, 160, 136, 0.15); display: flex; justify-content: center; align-items: center; }
.cc-cl-form-right { margin-right: -0.4rem; flex-shrink: 0; }


@media (max-width: 359px) {
    .cc-cl-form-row { flex-direction: column; }
    .cc-cl-form-left { padding-right: 0; width: 100%; flex: auto; order: -2; }
    .cc-cl-form-right { padding-top: 0.5rem; }
    .cc-cl-form-row .cc-code-copied-lbl { order: -1; }
}

.cc-copy-icon { width: 4.2rem; height: 4.2rem; font-size: 2rem; color: #171d2e; background: 0 0; display: flex ; align-items: center; justify-content: center; top: 0; right: 0; position: absolute; border: none;
border-radius: 0px .5rem .5rem 0px; }
.cc-share-row { display: flex; align-items: center; margin-top: 1.2rem; justify-content: center; }
.cc-share-lbl { color: #000; display: block; margin-left: 0.7rem; }
.cc-participate-wrap .live-stream-wrapper .h3 { margin-bottom: 0.8rem; }


/* .home-header */
.home-header-wrapper { position: fixed; top: 0; left: 0; z-index: 2; width: 100%; min-width: 32rem; }
.home-header { background-color: #1f1f1f; height: 4.4rem; padding: 0.7rem 2rem; color: var(--psin-white-static); display: flex;justify-content: space-between; align-items: center; }
.home-header-logo { width: 3rem; color: var(--psin-white-static); position:relative; z-index:11; }
.home-header-login { color: var(--psin-white-static); padding: 0.2rem 0.2rem 0 0.2rem; text-decoration: none; font-weight: 300; }

.home-header-register,
.home-logout-btn { margin-left: 4.4rem; padding: 0.2rem 1.4rem; }
.ps-header-social { margin-left: auto; margin-right: -0.5rem; position:relative; z-index:11; display: flex; align-items: center; }
.loginMenu > .dropdown-toggle > .btn { font-size: 1.4rem; padding: 0.5rem 1.4rem; }
.loginMenu > .dropdown-toggle > .btn > .btn-text { min-height: 2rem; }
.ps-share-link { padding: 0; display: flex; align-items: center; font-style: normal;font-weight: normal;font-size: 2.8rem;line-height: 1.33; text-decoration: none; color: var(--psin-white) !important; border: 0 !important; background-color: transparent !important; position: relative; }
.ps-share-icon { width: 2.8rem; flex-shrink: 0; height: 2.8rem; font-size: 2.8rem; display: flex; align-items: center; justify-content: center; }
.ps-share-text { font-style: normal;font-weight: normal;font-size: 2.4rem;line-height: 1.33; text-decoration: none; color: var(--psin-white); }
.ps-navbar-share { padding: 0; margin: 0; -webkit-margin-start: 0.4rem; margin-inline-start: 0.4rem; display: flex; align-items: center; }
.ps-navbar-share li { margin: 0 1.2rem 0 0; list-style: none; padding: 0; display: block; }
.ps-navbar-share li:last-child { margin-right: 0; }
.ps-navbar-share li::before { display: none; }
.ps-share-link.with-dot::before { width:28%; height:28%; top: 12%; right: 12%; border: 0.05em solid #1f1f1f !important; border-radius: 50%; -webkit-border-radius: 50%; -moz-border-radius: 50%; -ms-border-radius: 50%; -o-border-radius: 50%; position: absolute; z-index: 1; background-color: #FA3939; content: ''; display: block; }
.ps-share-link .ps-share-icon .cc_icon-notification svg:nth-child(2) { display: none; }
.ps-share-link.unsubscribe .ps-share-icon .cc_icon-notification svg:nth-child(1) { display: none; }
.ps-share-link.unsubscribe .ps-share-icon .cc_icon-notification svg:nth-child(2) { display: block; }

@media(max-width:359px){
    .ps-share-link { font-size: 2.2rem; }
    .ps-share-icon { width: 2rem; height: 2rem; font-size: 2rem; }
}


.shareMenu .dropdown-toggle::after { display: none; }
.shareMenu ul.dropdown-menu > li::before { display: none; }
.dropdown-menu.share-dd-menu { min-width: 22rem; width: 100%; border: none; background: none; }
.share-dd-box { padding: 2.5rem 1.6rem 2.4rem 1.6rem; background: #f5f5f5;border: 1px solid var(--psin-white);border-radius: 1.2rem; position: relative; z-index: 1; }
.shareMenu .dropdown-menu { width: 100%; top: 4.8rem !important; border-radius: 0;padding: 0;border: 0;background-color: #f5f5f5;color: #1F1F1F;position: absolute; }
.shareMenu .dropdown-item { display: flex;font-size: 1.4rem;line-height: 1.8rem;padding: 1.6rem 2.2rem;font-weight: 400;background-color: #f5f5f5;color: #1F1F1F; }
.shareMenu ul.share-dd-menu > li::before { display: none; }
.shareMenu .dropdown-item i { margin: 0 1.6rem 0 0; }
.shareMenu .dropdown-item:focus,
.shareMenu .dropdown-item:hover,
.shareMenu .dropdown-item.active,
.shareMenu .dropdown-item:active { color: #1F1F1F;background-color: var(--psin-white); }
.share-dd-menu::before { display: block; }

.ps-navbar-login { padding: 0; margin: 0; -webkit-margin-start: 8px; margin-inline-start: 8px; display: flex; align-items: center; }
.ps-navbar-login li { list-style: none; padding: 0; margin: 0; display: block; }
.ps-navbar-login li::before { display: none; }

.loginMenu .dropdown-toggle::after { display: none; }
.loginMenu ul.dropdown-menu > li::before { display: none; }
.loginMenu > .dropdown-toggle {  text-decoration: none !important; }
.loginMenu > .dropdown-toggle > .btn > i { font-size: 1.2rem; }

.loginMenu > .dropdown-toggle:hover .btn::before,
.loginMenu > .dropdown-toggle:focus .btn::before,
.loginMenu > .dropdown-toggle:active .btn::before { opacity: 1; }

.loginMenu > .dropdown-toggle { background-color: #0070CC; color: var(--psin-white); border-color: transparent; }
.loginMenu > .dropdown-toggle:hover .btn-primary { background-color: #0064b7; color: var(--psin-white); border-color: transparent; }
.loginMenu > .dropdown-toggle:focus .btn-primary { background-color: #0059a3; color: var(--psin-white); border-color: transparent; }
.loginMenu > .dropdown-toggle:active .btn-primary { background-color: #0059a3 !important; color: var(--psin-white) !important; border-color: transparent !important; }

.loginMenu > .dropdown-toggle .btn-primary::before,
.loginMenu > .dropdown-toggle:hover .btn-primary::before { box-shadow: 0 0 0 0.2rem #0064b7; }
.loginMenu > .dropdown-toggle:focus .btn-primary::before { box-shadow: 0 0 0 0.2rem #0059a3; }
.loginMenu > .dropdown-toggle:active .btn-primary::before { box-shadow: 0 0 0 0.2rem #0059a3; }

.dropdown-menu.login-dd-menu { min-width: 24.6rem; width: 100%; top: 4.7rem !important; right: -3rem; padding: 1.4rem 1rem;border: none; background: var(--psin-white); box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); border-radius: 1.5rem; -webkit-border-radius: 1.5rem; -moz-border-radius: 1.5rem; -ms-border-radius: 1.5rem; -o-border-radius: 1.5rem;
}
.dropdown-menu.login-dd-menu > li::before { display: none; }
.login-dd-menu > li { margin-bottom: 1.4rem; }
.login-dd-menu > li:last-child { margin-bottom: 0; }
.login-dd-menu > li > .btn { margin-top: 0; justify-content: flex-start; }
.login-dd-menu > li > .btn > i { font-size: 1.7rem; }
.login-dd-menu > li > .btn > .btn-text { flex: 1; width: 1%; text-align: center; display: flex; justify-content: center; }

.ps-tp-space { display: flex; align-items: center; }
.ps-tp-cell { padding: 0 0.7rem; }
.ps-tp-row { display: flex; align-items: center; }
.ps-tp-icon { font-size: 2.8rem; flex-shrink: 0; color: var(--psin-white); }
.ps-tp-text { margin-left: 0.7rem; font-size: var(--cc-font-size); line-height: 1.5; font-style: normal; font-weight: 700; color: var(--psin-white); display: block; }
.ps-profile-avtar { width: 2.6rem; flex-shrink: 0; margin: 0.1rem; }
.ps-profile-down-arrow { width: 1.4rem; font-size: 1.4rem; flex-shrink: 0; margin-left: 0.7rem; color: #fff; }

@media(max-width:359px){
    .ps-tp-icon { font-size: 2rem; }
    .ps-profile-avtar { width: 2rem; }
    .ps-profile-down-arrow { width: 1rem; font-size: 1rem; }
    .ps-tp-text { font-size: 1.2rem; }
}

.linkedMenu { padding: 0; margin: 0; list-style: none; display: block; }
.linkedMenu::before { display: none; }
.linkedMenu .dropdown-toggle::after { display: none; }
.linkedMenu ul.dropdown-menu > li::before { display: none; }
.linkedMenu > .dropdown-toggle { text-decoration: none !important; }
.linkedMenu > .ps-tp-row.dropdown-toggle .ps-profile-down-arrow i { transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -ms-transform: rotate(0deg); -o-transform: rotate(0deg); }
.linkedMenu > .ps-tp-row.dropdown-toggle.show .ps-profile-down-arrow i { transform: rotate(180deg); -webkit-transform: rotate(180deg); -moz-transform: rotate(180deg); -ms-transform: rotate(180deg); -o-transform: rotate(180deg); }

.dropdown-menu.linked-dd-menu { min-width: 24.6rem; width: 100%; top: 4.7rem !important; right: -3rem; padding: 1.4rem 1rem;border: none; background: var(--psin-white); box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); border-radius: 1.5rem; -webkit-border-radius: 1.5rem; -moz-border-radius: 1.5rem; -ms-border-radius: 1.5rem; -o-border-radius: 1.5rem; }
.dropdown-menu.linked-dd-menu > li::before { display: none; }
.linked-dd-menu > li { margin-bottom: 1.4rem; padding: 0; list-style: none; }
.linked-dd-menu > li:last-child { margin-bottom: 0; }
.linked-dd-menu > li > .btn { margin-top: 0; justify-content: flex-start; }
.linked-dd-menu > li > .btn > i { margin-right: 0.8rem; font-size: 1.7rem; }
.linked-dd-menu > li > .btn > .btn-text { display: flex; }
.check-linked-icon { margin-left: auto; flex-shrink: 0; width: 1.7rem; height: 1.7rem; color: #00A088; background-color: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; -webkit-border-radius: 50%; -moz-border-radius: 50%; -ms-border-radius: 50%; -o-border-radius: 50%; }
.check-linked-icon i { font-size: 1.7rem; }
.check-linked-icon.green-bg { color: #fff; background-color: #00A088; }
.check-linked-icon .cc_icon-close-2 { color: #FA3939; }
.linkedMenu .dropdown-menu .form-label { font-size: 1.7rem; line-height: 1.5; margin-bottom: 1.4rem; text-align: center; }

/* .home-header */


.nf-topbar-b.not-show { display:block; transform: translateY(-100%); will-change: visibility; visibility: hidden; transition: transform .25s cubic-bezier(0.4,0.0,0.2,1) ,visibility 0s linear .25s; }
.nf-topbar-b { padding:0.7rem; width:100%; top:0; left:0; position:absolute; z-index:11; display:block; visibility: visible; transform: translateY(0); transition: transform .25s cubic-bezier(0.4,0.0,0.2,1) ,visibility 0s linear 0s; }
.nf-topbar-b-box { padding:1.4rem 1.4rem 1rem 1.4rem; background:#FFF; box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.20); border-radius:1.5rem; -webkit-border-radius:1.5rem; -moz-border-radius:1.5rem; -ms-border-radius:1.5rem; -o-border-radius:1.5rem; }
.nf-topbar-b-header { margin-bottom:1.4rem; display:flex; align-items:center; }
.nf-topbar-b-header-text { margin: 0; color: #6B6B6B;font-size: 1.2rem;font-style: normal;font-weight: 400;line-height: 1.5; display:block; }
.nf-topbar-b-header-icon { margin-right:0.5rem; width:1.4rem; height:1.4rem; display:flex; align-items:center; justify-content:center; }
.nf-topbar-b-header-icon img { width:100%; height:auto; display:block; }
.nf-topbar-b-row { margin-bottom: 1.4rem; display:flex; }
.nf-topbar-b-icon { margin-right: 1.4rem; width:3.4rem; flex-shrink: 0; display:block; }
.nf-topbar-b-icon img { width:100%; height:auto; display:block; }
.nf-topbar-b-desc { flex:1; width:1%; }
.nf-b-title { margin: 0; font-style: normal;font-weight: 300;font-size: 1.7rem;line-height: 1.5;color: #000; display:block; }
.nf-b-text { margin: 0; font-style: normal;font-weight: 400;font-size: 1.4rem;line-height: 1.5;color: #000; display:block; }
.nf-topbar-b-btnrow { display:flex; }
.nf-topbar-b-btnrow .btn-spacer { margin: 0 -0.4rem 0 auto; display:flex; align-items:center; }
.btn-not-now { padding: 1rem 2rem; margin: 0; font-style: normal;font-weight: 700;font-size: 1.4rem;line-height: 1.333rem;color: #0070CC; text-decoration: none; display:block; }
.nf-topbar-b-btnrow .btn-primary { margin-left: 0; padding: 0.5rem 1.3rem; font-size:1.2rem; }

.cc-faq-head { padding: 2rem 0 1rem 0; }

/* faq accourdion */
.cc-accordion-wrapper { margin: 0; }
.cc-accordion { margin: 0; }
.cc-accordion:last-child { margin-bottom: 0; }
.cc-accordion-handle { width: 100%; min-height: 4.9rem; text-align: left; display: flex; align-items: center; position: relative; border: none; background: none; color: #000; font-weight: 400; font-size: 1.4rem; line-height: 1.5; margin: 0; padding: 1.4rem 3.2rem 1.4rem 1.4rem; transition: all 0.15s ease; -webkit-transition: all 0.15s ease; -moz-transition: all 0.15s ease; -ms-transition: all 0.15s ease; -o-transition: all 0.15s ease; }
.cc-accordion-handle i.cc_icon-minus-twex,
.cc-accordion-handle i.cc_icon-plus-twex { position: absolute; top: 1.7rem; right: 1.4rem; font-size: 1.4rem; color: #000; transition: all 0.15s ease; }
.cc-accordion-handle i.cc_icon-minus-twex { opacity: 0; transition: opacity 0.150s; }
.cc-accordion-handle[aria-expanded=true] i.cc_icon-minus-twex { opacity: 1; }
.cc-accordion-handle[aria-expanded=true] i.cc_icon-plus-twex { opacity: 0; }
.cc-accordion-handle:hover { color: #fff; background-color: #0070CC; }
.cc-accordion-handle:hover i.cc_icon-minus-twex,
.cc-accordion-handle:hover i.cc_icon-plus-twex { color: #fff; }
.cc-accordion-content { padding: 0.2rem 1.4rem 1.4rem 1.4rem; margin: 0; position: relative; }
.cc-accordion-content .text-a { font-size: 1.4rem; }
.cc-accordion-body { display: none; position: relative; }
.cc-accordion-body.show { display: block; }
.cc-accordion-wrapper .cc-accordion:nth-child(odd) { background-color: #F5F7FA; }

/* Footer */
html .gdk .footer-v2 {
    --dk-font: "sst",Arial,sans-serif;
    --dk-font-condensed: "sst condensed","sst",Arial,sans-serif;
    --dk-font-bebas-neue: "bebas neue","sst",Arial,sans-serif;
    --dk-font-mont: "Mont W05","sst",Arial,sans-serif
}

html[lang^=zh-hans] .gdk .footer-v2 {
    --dk-font: "PingFang SC","Hiragino Sans GB","Microsoft YaHei","WenQuanYi Micro Hei",Arial,sans-serif;
    --dk-font-condensed: "PingFang SC","Hiragino Sans GB","Microsoft YaHei","WenQuanYi Micro Hei",Arial,sans-serif;
    --dk-font-bebas-neue: "bebas neue","PingFang SC","Hiragino Sans GB","Microsoft YaHei","WenQuanYi Micro Hei",Arial,sans-serif
}

html[lang^=zh-hant] .gdk .footer-v2 {
    --dk-font: "å¾®è»Ÿæ­£é»‘é«”","Microsoft JhengHei","PMingLiu","æ–°ç´°æ˜Žé«”",sans-serif;
    --dk-font-condensed: "å¾®è»Ÿæ­£é»‘é«”","Microsoft JhengHei","PMingLiu","æ–°ç´°æ˜Žé«”",-apple-system,blinkmacsystemfont,sans-serif;
    --dk-font-bebas-neue: "bebas neue","å¾®è»Ÿæ­£é»‘é«”","Microsoft JhengHei","PMingLiu","æ–°ç´°æ˜Žé«”",-apple-system,blinkmacsystemfont,sans-serif
}

html[lang=ja-JP] .gdk .footer-v2,
html[lang=th-TH] .gdk .footer-v2 {
    --dk-font: "Hiragino Kaku Gothic Pro",Meiryo,Osaka,"MS PGothic",Arial,sans-serif;
    --dk-font-condensed: "Hiragino Kaku Gothic Pro",Meiryo,Osaka,"MS PGothic",Arial,sans-serif;
    --dk-font-bebas-neue: "bebas neue","Hiragino Kaku Gothic Pro",Meiryo,Osaka,"MS PGothic",Arial,sans-serif
}

html[lang=ko-KR] .gdk .footer-v2 {
    --dk-font: "Nanum Gothic","ë§‘ì€ê³ ë”•",Malgun Gothic,"ë‹ì›€",dotum,AppleGothic,Arial,sans-serif;
    --dk-font-condensed: "Nanum Gothic","ë§‘ì€ê³ ë”•",Malgun Gothic,"ë‹ì›€",dotum,AppleGothic,Arial,sans-serif;
    --dk-font-bebas-neue: "bebas neue","Nanum Gothic","ë§‘ì€ê³ ë”•",Malgun Gothic,"ë‹ì›€",dotum,AppleGothic,Arial,sans-serif
}

.gdk *, html {
    -webkit-font-smoothing: antialiased
}

.gdk .footer-v2 {
    margin: 0;
    -moz-osx-font-smoothing: grayscale;
    color: #1f1f1f;
    font-size: 15px;
    font-family: var(--dk-font);
    font-style: normal;
    font-weight: 400;
}

.gdk {
    position: relative; z-index: 0;
}
.gdk * {
    line-height: 1.5em;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box
}

.gdk div.pagebanner,
.gdk div.section {
    line-height: 0
}

.gdk a {
    text-decoration: none;
    background-color: transparent
}

.gdk [hidden] {
    display: none
}

svg:not(:root) {
    overflow: hidden
}


.gdk .site-footer-v2 a:focus:not(.focus-visible),.gdk .site-footer-v2  button:focus:not(.focus-visible) {
    border-radius: 0;
    outline-offset: 0.125em;
    box-shadow: none !important;
    outline: 2px solid var(--color-role-text-primary-base);
    outline-color: inherit;
}

.gdk .site-footer-v2  a:focus:not(:focus-visible),
.gdk .site-footer-v2  button:focus:not(:focus-visible) {
    outline: none;
}

.gdk .footer-v2 { font-size: 15px; }


.gdk .theme--dark { color: var(--color-role-text-primary-base); }
.gdk .theme--dark { background-color: var(--color-role-page-backgrounds-primary); }
.gdk .theme--dark a { color: var(--color-role-text-link-base); }
.gdk .box a { color: var(--color-role-text-link-base); }
.gdk .theme--dark.theme--dark a { color: var(--color-role-text-primary-base); }
.gdk .site-footer-v2 { min-width: 240px;-webkit-padding-before: var(--space-6);padding-block-start: var(--space-6);-webkit-padding-after: var(--space-5);padding-block-end: var(--space-5);overflow: hidden;position: relative;background: #00439c; }
.gdk .site-footer-v2, .gdk .site-footer-v2 a { color: var(--color-role-text-button-light); }
.gdk .site-footer-v2 a:hover { opacity: .7; }
.gdk .site-footer-v2 a:active { opacity: .6; }
.gdk .grid { display: grid;gap: var(--space-7) var(--space-5);-webkit-margin-after: var(--space-10);margin-block-end: var(--space-10);position: relative;z-index: 50;grid-template-columns: repeat(4,1fr);margin-left: var(--space-6);margin-right: var(--space-6); }

.gdk .site-footer-v2 .grid { margin-top: 0;margin-bottom: 0;row-gap: 0; }

.gdk .site-footer-v2 .grid { position: relative; }

.gdk .box { position: relative;z-index: 29;display: flex;flex-direction: column; }
.gdk .layout__1--a>* { grid-column: 1/-1; }
.gdk .site-footer-v2__brand-area { -webkit-padding-after: var(--space-5);padding-block-end: var(--space-5);position: relative; }

.gdk .site-footer-v2 .grid::after { content: '';bottom: 0;height: 1px;width: 100%;margin: 0 auto;position: absolute;background: rgba(255,255,255,.2); }
.gdk .site-footer-v2 .grid:last-child::after { display:none; }

.gdk .site-footer-v2 .grid .box:only-child { -webkit-padding-after: var(--space-6);padding-block-end: var(--space-6); }

.gdk .site-footer-v2__ps-logo {display: flex; }
.gdk .box>div { z-index: 29; }
.gdk .box>div:last-child { -webkit-margin-after: 0;margin-block-end: 0; }
.gdk .site-footer-v2 svg { fill: var(--color-role-text-primary-base-dark); }
.gdk .site-footer-v2 .nav-accordion__icon svg { width: 100%;height: 100%; }
.gdk .site-footer-v2__logo svg { height: var(--sticker-size-4);width: auto; }
.gdk .site-footer-v2__ps-logo svg { height: var(--space-9); }
.gdk .box>div:last-child>* { -webkit-margin-after: 0;margin-block-end: 0; }
.gdk .site-footer-v2__categories::after { content: '';bottom: 0;height: 1px;width: 100%;margin: 0 auto;position: absolute;background: rgba(255,255,255,.2); }
.gdk .site-footer-v2__categories li a { color: var(--color-role-text-button-light);-webkit-hyphens: auto;hyphens: auto;cursor: pointer;text-decoration: none;word-break: break-word; }
.gdk .site-footer-v2__categories .nav-list .social-links a { height: var(--icon-size-3);width: var(--icon-size-3);-webkit-margin-end: var(--space-2);margin-inline-end: var(--space-2);margin-top: 0;margin-bottom: 0;display: inline-block; }
.gdk .site-footer-v2__categories .nav-accordion:hover { opacity: .7; }

@media (min-width: 360px) {
    .gdk .grid { grid-template-columns: repeat(4,1fr);margin-left: var(--space-7);margin-right: var(--space-7); }
}

@media (orientation: landscape) {
    .gdk .grid { grid-template-columns: repeat(12,1fr);margin-left: var(--space-9);margin-right: var(--space-9); }
}


.gdk .site-footer-v2__container { -webkit-padding-before: var(--space-4);padding-block-start: var(--space-4); }
.gdk .site-footer-v2 .grid.site-footer-v2__container {grid-template-rows: auto;row-gap: var(--space-4); }
.gdk .site-footer-v2__categories { -webkit-padding-after: var(--space-4);padding-block-end: var(--space-4); }
.gdk .site-footer-v2__categories { position: relative; }
.gdk .site-footer-v2 .grid .\+links,
.gdk .site-footer-v2 .grid .\+social { grid-column: 1/-1; }

.gdk .txt-style-subtitle-bold { font-size: var(--text-3);font-weight: 600;line-height: 1.5em;--type-margin-top: var(--space-5);--type-margin-bottom: var(--space-5);-webkit-margin-before: var(--type-margin-top);margin-block-start: var(--type-margin-top);-webkit-margin-after: var(--type-margin-bottom);margin-block-end: var(--type-margin-bottom);color: var(--type-custom-color,var(--type-style-color));--type-style-color: var(--color-role-text-primary-base); }
.gdk .txt-style-subtitle-bold--m-0 { --type-margin-top: 0;--type-margin-bottom: 0; }
.gdk .site-footer-v2 .nav-accordion { font-size: var(--text-4);padding: 0;color: var(--color-role-text-button-light);width: 100%;border: none;display: flex;cursor: pointer;text-align: left;flex-direction: row;align-items: center;background: 0 0;font-family: var(--dk-font);justify-content: space-between; }
.gdk .site-footer-v2 .nav-accordion__icon { height: var(--icon-size-2);width: var(--icon-size-2);display: block;pointer-events: none;transition: transform .5s; }

.gdk .txt-style-utility { font-size: var(--text-2);font-weight: 400;line-height: 1.5em;--type-margin-top: var(--space-3);--type-margin-bottom: var(--space-3);-webkit-margin-before: var(--type-margin-top);margin-block-start: var(--type-margin-top);-webkit-margin-after: var(--type-margin-bottom);margin-block-end: var(--type-margin-bottom);color: var(--type-custom-color,var(--type-style-color));--type-style-color: var(--color-role-text-primary-base); }
.gdk .site-footer-v2__categories .nav-list { font-size: var(--text-3);-webkit-margin-before: var(--space-3);margin-block-start: var(--space-3); }
.gdk ul, .gdk li { margin: 0; }
.gdk .site-footer-v2__categories ul { padding: 0;list-style: none; }
.gdk .site-footer-v2__categories li { margin: 0;padding: 0;-webkit-padding-after: var(--space-3);padding-block-end: var(--space-3); }

@media (max-width: 767px) {
    .txt-style-utility.nav-list { display:none; }
    .grid.site-footer-v2__container .box.site-footer-v2__categories:first-child .txt-style-utility.nav-list { display:block; }
    .gdk .site-footer-v2 .nav-accordion .nav-accordion__icon.ps-utility-plus { transition:all 0.5s ease; }
    .gdk .site-footer-v2 .nav-accordion.icon-rotate .nav-accordion__icon.ps-utility-plus { transform:rotate(45deg); }
}

.gdk .site-footer-v2__logos.grid { padding-top: var(--space-5);padding-bottom: var(--space-5);display: flex;flex-direction: row-reverse;justify-content: space-between; }
.gdk .media-block--image { --custom-mobile-width: 100%;--custom-tablet-width: 100%;--custom-desktop-width: 100%;width: var(--custom-mobile-width); }
.gdk .media-block { display: block;position: relative;--icon-block-theme: var(--color-role-text-primary-base-dark);margin: 0;-webkit-margin-after: var(--space-7);margin-block-end: var(--space-7); }
.gdk .site-footer-v2__badges .media-block { -webkit-margin-after: 0;margin-block-end: 0; }
.gdk .media-block__inner { position: relative; }
.gdk figure {margin: 0;}
.gdk .media-block__figure { width: 100%;overflow: hidden;position: relative;margin: 0;display: flex; }
.gdk .lozad { will-change: height,width,filter; }
.gdk .media-block__figure picture{ width: 100%;height: 100%;position: relative; }
.gdk .lozad[data-loaded=true] {animation-name: fadeIn;animation-iteration-count: 1;animation-duration: var(--animation-duration,.75s);animation-delay: 0s;animation-timing-function: ease;animation-fill-mode: both;backface-visibility: hidden;animation-direction: normal; }

.gdk .site-footer-v2__logo {display: flex;flex-direction: column;align-items: flex-start; }
.gdk .site-footer-v2__copyright>* { font-size: var(--text-3);margin: 0; }

@keyframes fadeIn {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

.gdk .site-footer-v2__country { padding-left: 0;padding-right: 0;-webkit-padding-before: var(--space-5);padding-block-start: var(--space-5);-webkit-padding-after: var(--space-4);padding-block-end: var(--space-4);border: none;display: flex;flex-direction: column;background: 0 0;align-items: flex-start;justify-content: space-between; }
.gdk .txt-style-secondary { font-size: var(--text-3);font-weight: 400;line-height: 1.5em;--type-margin-top: var(--space-5);--type-margin-bottom: var(--space-5);-webkit-margin-before: var(--type-margin-top);margin-block-start: var(--type-margin-top);-webkit-margin-after: var(--type-margin-bottom);margin-block-end: var(--type-margin-bottom);color: var(--type-custom-color,var(--type-style-color));--type-style-color: var(--color-role-text-primary-base); }
.gdk .txt-style-secondary--m-0 { --type-margin-top: 0;--type-margin-bottom: 0; }
.gdk .site-footer-v2__country .site-footer-v2__country--selector { width: 100%; }
.gdk .site-footer-v2__country a {display: flex;align-items: center; }
.gdk .site-footer-v2__country svg { height: var(--icon-size-2);width: var(--icon-size-2);margin-left: var(--space-3);margin-right: var(--space-3); }
.gdk .site-footer-v2__country svg:first-child { height: var(--icon-size-3);width: var(--icon-size-3);-webkit-margin-start: 0;margin-inline-start: 0; }
.gdk .txt-style-utility { font-size: var(--text-2);font-weight: 400;line-height: 1.5em;--type-margin-top: var(--space-3);--type-margin-bottom: var(--space-3);-webkit-margin-before: var(--type-margin-top);margin-block-start: var(--type-margin-top);-webkit-margin-after: var(--type-margin-bottom);margin-block-end: var(--type-margin-bottom);color: var(--type-custom-color,var(--type-style-color));--type-style-color: var(--color-role-text-primary-base); }
.gdk .txt-style-utility--m-0 { --type-margin-top: 0;--type-margin-bottom: 0; }
.gdk .site-footer-v2__legal { font-size: var(--text-3);padding: 0;display: flex;list-style: none;align-items: flex-start; }
.gdk .site-footer-v2__country .site-footer-v2__legal { width: 100%; }
.gdk .site-footer-v2__legal li{ -webkit-margin-start: var(--space-5);margin-inline-start: var(--space-5);-webkit-margin-after: 0;margin-block-end: 0;display: flex;position: relative;align-items: center; padding-left: 0; }
.gdk .site-footer-v2__legal li::after { content: '';-webkit-margin-start: calc(-1*(var(--space-5)/2));margin-inline-start: calc(-1*(var(--space-5)/2));top: 50%;height: var(--space-4);position: absolute;transform: translate(0,-50%);border-left: 1px solid var(--color-role-text-primary-base); }
.gdk .site-footer-v2__legal li:first-child { -webkit-margin-start: 0;margin-inline-start: 0; }
.gdk .theme--dark ul li:after { color: var(--color-role-text-primary-base); }
.gdk .footer-v2 ul li:before { display: none; }

@media (max-width: 767px) {
    .gdk .site-footer-v2__country .site-footer-v2__legal { padding-left: 0.8em;flex-wrap:wrap; }
   .gdk .site-footer-v2__legal li { -webkit-margin-start: 0; margin-inline-start: 0; -webkit-margin-end: var(--space-5); margin-inline-end: var(--space-5); }
}

/* sony-bar */
.sony-bar { padding: 0; height: 36px; width: 100%; min-width: 320px; display: flex; justify-content: flex-end; z-index: 1; background-color: #000; overflow: hidden; }
.sony-bar a { display: block; color: #fff; }
.sony-bar a:focus-visible { outline-offset: -0.4rem; }
.sony-logo { display: block; height: 100%; width: 10.5rem; background-image: url(../gfx/sony_logo-r.svg); background-repeat: no-repeat; background-position: center center; margin: 0 8px; }


.sony-bar {
    top: 0;
    left: 0;
    font-size: 0;
    height: 36px;
    position: relative;
    width: 100%;
    z-index: 111;
}
.sony-bar {
    background-color: #000;
}
[dir=ltr] .sony-bar {
    text-align: right;
}
.sony-logo {
    display: inline-block;
    height: 100%;
    width: 100%;
}
.sony-bar__logo {
    width: 75px;
}
.sony-logo {
    background-image: url(../gfx/sony_logo-footer2023.svg);
    background-repeat: no-repeat;
    background-position: center;
}
.sony-bar__logo {
    margin: 0 8px;
}
@media screen and (max-width: 699px) {
    body > .sony-bar {
        display: none;
    }
}
.sie-logo { padding-bottom: 0.5em; }



.cc-trophies-page { position: fixed; width: 100%; height: 100%; top: 0; z-index: 2; display: none; min-height: 100vh; min-width: 32rem;  }
.cc-trophies-page-in { overflow: hidden; overflow-y: auto; max-height: 100%; padding: 4.6rem 2rem; z-index: 11;  }
.cc-trophies-bg { position: fixed; }
.cc-trophies-bg::after { background: linear-gradient(0deg, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.40) 100%); display: block; content: ''; position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1; }
.dop-logo { --bs-aspect-ratio: 48.48484848484848%; max-width: 16.5rem; display: block; margin-bottom: 3.2rem; }
.cc-trophies-box { border-radius: 1.5rem; background: linear-gradient(152deg, #935DFB 0%, #4C76FE 50%, #00B0FB 100%); box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.10); padding: 1.6rem; position: relative; z-index: 1; }
.cc-trophies-head { border-bottom: 0.1rem solid #fff; margin-bottom: 1.4rem; color: #fff; padding-bottom: 0.8rem; }
.cc-trophies-main-row { display: flex; flex-direction: column; flex-wrap: wrap; padding-bottom: 1.4rem; }
.cc-trophies-main-l { margin-bottom: 1.4rem; display: flex; align-items: center; }

.cc-trophy-point-row { display: flex; align-items: center; flex-wrap: wrap; margin: 0 -0.2rem; }

.cc-trophy-point-cell { padding: 0 0.2rem; display: flex; align-items: center; min-width: 7.6rem; }

.cc-trophy-icon { font-size: 3.4rem; max-width: 1em; flex: 0 0 1em; width: 1em; height: 1em; flex-shrink: 0; display: flex; align-items: center; justify-content: center; margin-right: 0.3rem; }
.cc-trophy-point-count { display: block; align-self: flex-end; color: #fff; }
.cc-trophy-point-count-text { display: block; margin: 0; }

.cc-trophies-leval-row { display: flex; align-items: center; margin-right: 1.6rem; }
.cc-trophies-leval-icon { width: 6.4rem; flex-shrink: 0; margin-right: 0.8rem; }
.cc-trophies-leval-count-text { font-weight: 300; display: block; margin: 0; color: #fff; min-width: 8.5rem; }
.cc-trophies-leval-total-text { font-weight: 300; display: block; margin: 0; color: #fff; }
.cc-trophies-leval-total-count { font-weight: 300; display: block; margin: 0; color: #fff; }
.trophies-ticket { --bs-gutter-y: 1.4rem; }
.cc-trophies-pluse { position: relative; width: 6.4rem; font-size: 6.4rem; }
.cc-trophies-pluse-check { display: flex; align-items: center; justify-content: center; width: 3rem; height: 3rem; position: absolute; bottom: 0; right: -0.8rem; background-color: #fff; border-radius: 50%; font-size: 3rem; color: #00A088; z-index: 1; }
.cc-tiket-result-row { display: flex; align-items: center; }
.cc-tiket-result-row .cc-trophies-pluse { margin-right: 0.7rem; }
.cc-tiket-result-count { display: flex; align-items: center; flex-wrap: wrap; }
.cc-tiket-result-text,
.cc-tiket-result-x { font-weight: 300; display: block; margin: 0; color: #fff; }
.cc-tiket-result-x { margin-right: 0.7rem; }

.cc-animation-group { position: relative; min-width: 8.5rem; height: 1.25em; overflow: hidden; margin: 0; }
.cc-animation-inner { position: absolute; top: 0; left: 0; width: 100%; }
.cc-animation-inner span { display: block; height: 1.25em; }
.cc-trophy-point-count .cc-animation-group { min-width: 3.4rem; }

.cc_icon-bronze::before { background-image: url(../gfx/cc-trophy-bronze.png); background-size: contain; background-repeat: no-repeat; }
.cc_icon-silver::before { background-image: url(../gfx/cc-trophy-silver.png); background-size: contain; background-repeat: no-repeat; }
.cc_icon-gold::before { background-image: url(../gfx/cc-trophy-gold.png); background-size: contain; background-repeat: no-repeat; }
.cc_icon-platinum::before { background-image: url(../gfx/cc-trophy-platinum.png); background-size: contain; background-repeat: no-repeat; }
.cc_icon-ps-plus::before { background-image: url(../gfx/cc-trophy-ps-plus.svg); background-size: contain; background-repeat: no-repeat; }
.cc_icon-ticket-gold::before { background-image: url(../gfx/cc_icon-ticket.svg); background-size: contain; background-repeat: no-repeat; }



/* transparent box */
.cc-hero-transparent-box{padding: 1.6rem; background: rgba(0, 0, 0, 0.15); border-radius: 1.5rem; box-shadow: 0 0.5rem 1.5rem 0 rgba(0, 0, 0, 0.10); -webkit-backdrop-filter: blur(0.25rem); backdrop-filter: blur(0.25rem); margin-bottom: 1.4rem;}
.cc-hero-transparent-box .text-a >*{margin-bottom: 0.7rem;}
.cc-hero-transparent-box .text-a >*:last-child{margin-bottom:0;}

/* cc controller card */
.cc-controller-card{display: flex; flex-direction: column; align-items: center; text-decoration: none; padding: 0.8rem 0.8rem 1.2rem 0.8rem; background-color: #FFF; box-shadow: 0 0.5rem 1.5rem 0 rgba(0, 0, 0, 0.10); border-radius: 1.5rem; margin-bottom: 2.2rem; transition: box-shadow 0.25s ease-in-out; color: #171d2e !important;}
.cc-controller-gfx{max-width: 33.3rem; border-radius: 1.5rem; overflow: hidden; margin-bottom: 1.4rem;}
.cc-controller-card .text-a .h5{ color: #0070cc; }
.cc-controller-card:hover{box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.50);}

.mlr-20 { margin: 0 -2.0rem; }
.guess-card-row{--bs-gutter-x: 1.4rem; --bs-gutter-y: 1.4rem;}
.cc-guess-section {padding: 4rem 0; }
.cc-guess-section.cc-guess-hero{position: relative; padding-top: 2rem; background: linear-gradient(180deg,rgba(109, 70, 201, 1) 0%, rgba(34, 33, 186, 1) 50%);}
.cc-guess-hero-bg{position: absolute;top: 0; left: 0; right: 0; bottom: 0; z-index: 0; display: block;}

.guess-card-swiper{overflow: visible; margin: 0 -0.7rem;;}
.guess-card-swiper-wrapper{ max-width: 88.8%; width: 100%; margin: auto;}
.guess-card-swiper .swiper-slide{ padding: 0 0.7rem; display: flex; flex-direction: column; height: auto;}
.cc-hero-wrapper{padding: 0 2rem; position: relative;}
.cc-hero-wrapper .h3{margin-bottom: 1.4rem;}

.guess-card-box{background-color: #FFF; text-align: center; text-decoration: none; display: flex; flex: 1 1 auto;  padding: 0.8rem; border-radius: 1.5rem;text-align: left; box-shadow: 0 0.5rem 1.5rem 0 rgba(0, 0, 0, 0.10); transition: box-shadow 0.25s ease-in-out;}
.guess-card-box:hover{ box-shadow: 0 0.5rem 1.5rem 0 rgba(0, 0, 0, 0.50);}
.guess-card-gfx{ border-radius:1.5rem; width: 43.54354354354354%; max-width: 12.9rem ;  overflow: hidden; margin-right: 1.4rem;  align-self: flex-start;    background: lightgray 0px -42.386px / 100% 133.333% no-repeat;}
.guess-card-gfx img{width: 100%;}

.guess-card-box .text-a{align-items: flex-start; flex: 1;padding: 0.8rem 0;}
.guess-card-box .text-a > .h5{color: #000;}
.guess-card-box .text-a > *{color: #6B6B6B;}

.guess-card-box .btn-wrapper{margin-top:auto; width: 100%;}
.guess-card-box .btn-wrapper .btn{margin-left: 0;}
.guess-card-box.disabled { pointer-events: none; }
.guess-card-box .text-a .h5{ margin-bottom: 0.2rem;}
.ratio.contain img{-o-object-fit: contain;object-fit: contain;}
.guess-card-swiper-wrapper .guess-card-pagination,
.days-of-play-swiper .days-of-play-pagination{position: static;  height: 1.5rem; width: 100%; display: flex; align-items: center; justify-content: center; margin-top: 0.8rem; margin-bottom: -2.6rem; }

.guess-card-pagination .swiper-pagination-bullet,
.days-of-play-swiper .swiper-pagination-bullet{ position: relative; display: flex; align-items: center; justify-content: center; background-color: transparent; --swiper-pagination-bullet-border-radius: 0.2rem; --swiper-pagination-bullet-width:1.5rem; --swiper-pagination-bullet-horizontal-gap: 0.5rem; --swiper-pagination-bullet-height:1.5rem; --swiper-pagination-color:#0070CC; --swiper-pagination-bullet-inactive-color:#F5F7FA; --swiper-pagination-bullet-inactive-opacity:1; border-radius: 0.2rem !important; -webkit-border-radius: 0.2rem !important; -moz-border-radius: 0.2rem !important; -ms-border-radius: 0.2rem !important; -o-border-radius: 0.2rem !important; }

.guess-card-pagination .swiper-pagination-bullet::before,
.days-of-play-pagination .swiper-pagination-bullet::before{ content: ""; height: 0.3rem; background-color: #F5F7FA; display: block; width: 100%;}
.guess-card-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active::before,
.days-of-play-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active::before{background-color: #0070CC;}

/* winner-card */
.cc-guess-section.cc-winner-section{padding-bottom: 2rem; padding-top: 2rem; margin-inline: -2rem;}
.cc-guess-section.cc-winner-section .winner-card-wrapper .text-a .h6{font-weight: 400; color: #000; margin-bottom: 1.4rem;}
.winner-card{padding: 0.8rem 0.8rem 1.6rem 0.8rem; text-align: center; border-radius: 1.5rem; background-color: #FFF; height: 100%; box-shadow: 0 0.5rem 1.5rem 0 rgba(0, 0, 0, 0.10);}

.winner-gfx-wrapper{padding: 0.8rem 0.8rem 1.6rem 0.8rem; margin-bottom: 1.4rem;background: rgba(255, 255, 255, 0.15); box-shadow: 0 0.5rem 1.5rem 0 rgba(0, 0, 0, 0.20); border-radius: 1.5rem;}
.winner-gfx{--bs-aspect-ratio:64.15929203539823%; margin-bottom: 1.4rem; border-radius: 1.5rem; overflow: hidden;}
.winner-gfx img{height: 100%; width: 100%;}

.user-identity-box{display: flex; align-items: center; justify-content: center;}
.user-logo{max-width: 3.6rem; margin-right: 0.8rem;}
.user-logo img{border-radius: 50%;}
.user-identity-box .h6{ margin: 0.925rem 0;}
.swiper-button-prev:after, .swiper-rtl .swiper-button-next:after,
.swiper-button-next:after, .swiper-rtl .swiper-button-prev:after,
.swiper-button-prev:after, .swiper-rtl .days-of-next:after,
.swiper-button-next:after, .swiper-rtl .days-of-prev:after{content: "";}
.winner-card-swiper-wrapper{ max-width: 68.8%; margin: auto; width: 100%;}
.winner-card-swiper{overflow: visible; margin: 0 -0.7rem;}
.winner-card-swiper .swiper-slide{padding: 0 0.7rem; height: auto; display: flex; flex-direction: column;}
.winner-card-swiper .winner-card-wrapper{display: flex; flex-direction: column; flex: 1 1 auto;}

.winner-card-swiper .winner-card-prev,
.winner-card-swiper .winner-card-next,
.days-of-play-swiper .days-of-prev,
.days-of-play-swiper .days-of-next{position: absolute; width: 4.2rem; height: 4.2rem; margin: 0; --swiper-navigation-top-offset:calc(50% - 3.15rem)}
.winner-card-swiper .winner-card-prev,
.days-of-play-swiper .days-of-prev{right: calc(100% - -0.3rem); left: auto;}
.winner-card-swiper .winner-card-next,
.days-of-play-swiper .days-of-next{ left: calc(100% - -0.3rem);right: auto;}
.winner-card-swiper .winner-card-prev:focus-visible,
.winner-card-swiper .winner-card-next:focus-visible,
.days-of-play-swiper .days-of-prev:focus-visible,
.days-of-play-swiper .days-of-next:focus-visible{ border-radius: 50%;}
.winner-card .text-a .h6{font-weight: 400;}
.winner-card .text-a .h6,
.winner-card .text-a .h4{margin-bottom: 1.4rem;}
.winner-card .text-a{font-size: 1.2rem;}
.winner-card ol{ columns: 2; column-gap: 1rem; }
.winner-card ol li{overflow: hidden; overflow-wrap: break-word;  text-align: left;}
.winner-card ul > li, .winner-card ol > li{-webkit-padding-start: 2rem;padding-inline-start: 2rem;}
.winner-card ol > li::before{color: #000;}

/* live-stream */
.cc-guess-section.cc-live-stream{padding-top: 2rem; padding-bottom: 2rem;}
.live-stream-wrapper{padding: 1.3rem 1.6rem 1.6rem 1.6rem; background-color:#F5F7FA; border-radius: 1.5rem; margin: 0 auto; overflow: hidden; }
.live-stream-wrapper .text-a >.h1{ margin-bottom: 0.8rem; color: #000; }
.live-stream-wrapper .text-a >*{color: #6B6B6B;}
.ak-livestream-row { margin-inline:-0.8rem; display: flex; align-items: center}
.ak-livestream-cell {padding: 0 0.8rem; width: 50%; max-width: 50%; flex: 0 0 50% }
.ak-livestream-box {padding: 0.7rem 0.7rem; border-radius: 1.5rem; background-color: #171d2e; text-decoration: none; display: flex; align-items: center }
.ak-livestream-box.twitch {background-color: #7c3cdd;}
.ak-livestream-box.twitch:hover { background-color: #6500ff}
.ak-livestream-box.instagram { background-color: #db046d }
.ak-livestream-box.instagram:hover { background-color: #ff047f;}
.ak-livestream-box-icon {margin-right: 0.7rem; width: 2rem; height: 2rem; font-size: 2rem; color: #fff; display: flex; align-items: center; justify-content: center }
.ak-livestream-box-text {margin: 0; color: #fff;font-size: 1.2rem; font-style: normal; font-weight: 700; line-height: 1; display: block }

@media (min-width: 360px) {
    .ak-livestream-box-icon {width:3.4rem;height: 3.4rem; font-size:3.4rem}
    .ak-livestream-box-text {font-size: 1.4rem}
}

/* heading background */
.cc-pd-tagname { display: flex; justify-content: center; margin-bottom: 1.4rem; }
.cc-pd-tagname-box { padding: 0.7rem 1.5rem 0.4rem 1.5rem; display: flex; position: relative; background-color: #0070CC; z-index: 1; transform: skewX(-15deg); -webkit-transform: skewX(-15deg); -moz-transform: skewX(-15deg); -ms-transform: skewX(-15deg); -o-transform: skewX(-15deg); }

.cc-pd-tagname-box-in { padding: 0; font-size: 1.7rem; line-height: 1.5; font-weight: 300; color: #FFF; display: flex; align-items:center; position: relative; z-index: 11; transform: skewX(15deg); }

/* days-of-play-swiper */
.cc-guess-section.cc-days-of-play{padding-bottom: 3.7rem; padding-top: 2rem;}
.cc-guess-section.cc-days-of-play > .text-a{color: #6B6B6B; margin-bottom: 1.4rem;}
.cc-guess-section.cc-days-of-play > .text-a .h2{color: #000; margin-bottom: 1.4rem;}
.days-of-play-swiper{overflow: visible; margin: 0 -0.7rem;}
.days-of-play-swiper .swiper-slide{padding: 0 0.7rem; height: auto; display: flex; flex-direction: column;}

.days-of-play-gfx{overflow: hidden; border-radius: 1.5rem; box-shadow: 0 0.5rem 1.5rem 0 rgba(0, 0, 0, 0.20); margin-bottom: 1.4rem; justify-self: center;}
.day-of-play-price{display: block; font-size: 2rem; font-weight: 400; line-height: 1.25;}
.days-of-play-swiper .winner-card{padding-bottom: 1.2rem; display: flex; flex-direction: column; flex: 1 1 auto; text-decoration: none; transition: box-shadow 0.25s ease-in-out;}
.days-of-play-swiper .winner-card:hover { box-shadow: 0 0.5rem 1.5rem 0 rgba(0, 0, 0, 0.50);}
.days-of-play-swiper .winner-card:focus-visible { box-shadow: 0 0 0 0.2rem #000;}
.days-of-play-swiper .winner-card .text-a{align-items: flex-start; text-align: left; padding:0 0.8rem; flex: 1;}
.days-of-play-swiper .winner-card .text-a .btn-wrapper{margin-top: auto; margin-left: -0.4rem;}
.days-of-play-swiper .winner-card .text-a .btn-wrapper .btn{margin-top: 0;}
.days-of-play-swiper .days-of-play-pagination{ margin-top: 2.2rem;}

/* notification */
.cc-nf-row { padding: 1.4rem; border-bottom: 0.1rem solid #F5F7FA; display: flex; align-items: center; }
.cc-nf-row.unread { background-color: #F5F7FA; }
.cc-nf-icon { flex-shrink: 0; margin-right: 1.4rem; font-size: 2.8rem; color: #000; display: block; }
.cc-nf-cell { padding: 0; flex: 1; width: 1%; align-self: center; }
.cc-nf-desc { color: #000; }

.form-error { display: none; color:red; }

/*cookie banner*/
.evidon-banner { font-size: 1.6rem !important; }
.evidon-banner div:last-child { display: none; }
.evidon-banner #_evh-ric-c:hover { border: none!important; outline: none!important }
.evidon-banner-message { font-size: 16px !important; line-height: 22.5px !important; }
.evidon-banner-acceptbutton,
.evidon-banner-declinebutton { font-size: 1em !important; line-height: 1 !important; padding: 0.75em 1em !important; border-radius: 2em !important; font-weight: 600 !important; width: 152px !important; }
.evidon-banner-message a { color:#fff; }
.evidon-banner-declinebutton:hover,
.evidon-banner-optionbutton:hover { outline: none; -webkit-box-shadow: 0 0 0 2px #363636!important; box-shadow: 0 0 0 2px #363636!important; }
.evidon-banner-acceptbutton:hover { outline: none; -webkit-box-shadow: 0 0 0 2px #eee!important; box-shadow: 0 0 0 2px #eee!important; }

@media (min-width: 1920px) {
    .evidon-banner { font-size: 1.8rem !important; }
}

@media (max-width: 767px) {
    .evidon-banner-message { padding-left: 80px !important; }
    .evidon-banner-image { left: 20px !important; }
    .evidon-banner-acceptbutton, .evidon-banner-declinebutton { width: auto !important; display: inline-block !important; }
    .evidon-banner-declinebutton { margin-left: 80px !important; }
    .evidon-banner-acceptbutton { margin-left: 15px !important; }
    .evidon-banner-message a { color:#fff; }
    .evidon-banner-declinebutton:hover, .evidon-banner-optionbutton:hover { outline: none; -webkit-box-shadow: 0 0 0 2px #363636!important; box-shadow: 0 0 0 2px #363636!important; }
    .evidon-banner-acceptbutton:hover { outline: none; -webkit-box-shadow: 0 0 0 2px #eee!important; box-shadow: 0 0 0 2px #eee!important; }
}

@media (max-width: 335px) {
    .evidon-banner-message { padding-left: 70px !important; }
    .evidon-banner-image { left: 10px !important; }
    .evidon-banner-message { font-size: 14px !important; line-height: 20.5px !important;  }
    .evidon-banner-declinebutton { margin-left: 70px !important; }
    .evidon-banner-acceptbutton { margin-left: 5px !important; }
}
