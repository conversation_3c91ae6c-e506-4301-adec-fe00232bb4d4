-- SQL statements to create indexes for optimizing query performance

-- psn_users table
-- user_id is frequently used in WHERE clauses in related models
CREATE INDEX IF NOT EXISTS idx_psn_users_id ON psn_users(id);
-- These fields already have indexes defined in the model
-- online_id, user_uuid, country_code, nickname, fake_user, deleted_at

-- tickets table
-- user_id is frequently used in WHERE clauses but doesn't have an index
CREATE INDEX IF NOT EXISTS idx_tickets_user_id ON tickets(user_id);
-- Combined index for common query patterns in PsnUser model
CREATE INDEX IF NOT EXISTS idx_tickets_user_id_identifier ON tickets(user_id, identifier);
-- Index for timestamp filtering
CREATE INDEX IF NOT EXISTS idx_tickets_created_at ON tickets(created_at);
-- These fields already have indexes defined in the model
-- ticket, identifier, type, is_view

-- participants table
-- user_id and competition_id are frequently used in WHERE clauses but don't have indexes
CREATE INDEX IF NOT EXISTS idx_participants_user_id ON participants(user_id);
CREATE INDEX IF NOT EXISTS idx_participants_competition_id ON participants(competition_id);
-- is_winner is frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_participants_is_winner ON participants(is_winner);
-- Combined indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_participants_competition_id_is_winner ON participants(competition_id, is_winner);
CREATE INDEX IF NOT EXISTS idx_participants_competition_id_is_winner_prize_type ON participants(competition_id, is_winner, prize_type);
CREATE INDEX IF NOT EXISTS idx_participants_competition_id_is_winner_tickets ON participants(competition_id, is_winner, tickets);
CREATE INDEX IF NOT EXISTS idx_participants_day_is_winner_tickets ON participants(day, is_winner, tickets);
CREATE INDEX IF NOT EXISTS idx_participants_is_winner_updated_at_user_id ON participants(is_winner, updated_at, user_id);
-- Index for timestamp filtering
CREATE INDEX IF NOT EXISTS idx_participants_updated_at ON participants(updated_at);
-- These fields already have indexes defined in the model
-- tickets

-- competitions table
-- date is frequently used in WHERE clauses but doesn't have an index
CREATE INDEX IF NOT EXISTS idx_competitions_date ON competitions(date);
-- These fields already have indexes defined in the model
-- code

-- memories table
-- These fields already have indexes defined in the model
-- day, code

-- trivia table
-- day is frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_trivia_day ON trivia(day);

-- guess_the_games table
-- day is frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_guess_the_games_day ON guess_the_games(day);

-- math_riddles table
-- day is frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_math_riddles_day ON math_riddles(day);

-- find_the_mistakes table
-- day is frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_find_the_mistakes_day ON find_the_mistakes(day);

-- day_of_play_offers table
-- sort_order is frequently used in ORDER BY clauses
CREATE INDEX IF NOT EXISTS idx_day_of_play_offers_sort_order ON day_of_play_offers(sort_order);

-- faqs table
-- is_display and sort_order are frequently used in WHERE and ORDER BY clauses
CREATE INDEX IF NOT EXISTS idx_faqs_is_display ON faqs(is_display);
CREATE INDEX IF NOT EXISTS idx_faqs_sort_order ON faqs(sort_order);
-- Covering index for common query pattern
CREATE INDEX IF NOT EXISTS idx_faqs_is_display_sort_order_question_answer ON faqs(is_display, sort_order, question(255), answer(255));

-- app_stats table
-- key and day are frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_app_stats_key_day ON app_stats(key, day);
-- Covering index for common query pattern
CREATE INDEX IF NOT EXISTS idx_app_stats_key_day_value ON app_stats(key, day, value(255));

-- Indexes for foreign key relationships
-- These help with JOIN operations
CREATE INDEX IF NOT EXISTS idx_tickets_transaction_id ON tickets(transaction_id);
CREATE INDEX IF NOT EXISTS idx_participants_user_id_competition_id ON participants(user_id, competition_id);

-- Note: Some indexes might already exist in the database even if not explicitly defined in the models.
-- Before applying these changes in production, it's recommended to:
-- 1. Check existing indexes with SHOW INDEXES FROM table_name
-- 2. Analyze query performance with EXPLAIN before and after adding indexes
-- 3. Consider the impact on write operations, as indexes can slow down INSERT, UPDATE, and DELETE operations
-- 4. Monitor index usage with SHOW INDEX FROM table_name to identify unused indexes
-- 5. Consider dropping unused indexes to improve write performance
-- 6. For covering indexes on TEXT fields, we've limited the indexed length to 255 characters
--    to avoid creating excessively large indexes
-- 7. Consider the order of columns in composite indexes based on the selectivity of each column
--    (most selective columns should come first)
-- 8. For high-write tables, consider using fewer indexes and focusing on the most critical queries
