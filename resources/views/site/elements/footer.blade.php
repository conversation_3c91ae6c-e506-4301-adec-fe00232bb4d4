@if($page_type != "dashboard" || $is_mobile)
    @include('site.elements.ps-footer')
@endif


@if($page_type == "dashboard" && !$is_mobile)

    @php  $site_pages_arr = site_pages_arr();  @endphp

    @foreach ($site_pages_arr as $sitePageSlug => $sitePageArr)

        @php  $make_page_active = $active_page == $sitePageSlug ? true : false;  @endphp

        <div class="offcanvas offcanvas-end cc-{{ $sitePageSlug }} {{ in_array($sitePageSlug, ['faq', 'notifications', 'participate', 'participate-thank-you']) ? ' white-bg' : '' }}{{ $make_page_active ? ' show' : '' }}" data-bs-scroll="true" id="cc-{{ $sitePageSlug }}" aria-labelledby="cc-{{ $sitePageSlug }}Label">

            @if($sitePageArr['add_page'] || $make_page_active)
                @include("site.desktop.pages." . $sitePageSlug)
            @endif

        </div>
        
    @endforeach
    
@endif


@if($page_type == "dashboard" && $is_mobile)

    @include('site.mobile.elements.splash-screen')

    @include('site.mobile.elements.install-popup')

@endif

<input type="hidden" id="is_mobile" value="{{ $is_mobile ? 'Y' : 'N' }}">