<?php

namespace App\_modules\ChatBotMessageParser\Prompts;

use Exception;

class PollPrompt{
    /**
     * @param string $questionStr
     * @param array  $pollOptions
     * @param array  $userMap
     *
     * @return string
     * @throws Exception
     */
    public static function getUserPrompt(string $questionStr = "", array $pollOptions = [], array $userMap = []): string{
        $userMapStr     = json_encode($userMap, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT);
        $pollOptionsStr = json_encode($pollOptions, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT);

        return "
            Die Umfrage-Frage lautet: \"$questionStr\"
            Die möglichen Antwortoptionen sind: $pollOptionsStr
            Die Antworten der User sind:
            $userMapStr

            Analysiere die Antworten der User, um festzustellen, welche Antwortoption gewählt wurde.
            Beachte, dass die Antworten aus einer Chat-Umgebung stammen und möglicherweise Tippfehler enthalten.
            Du musst in der Lage sein, solche Varianten zu identifizieren und zu interpretieren. Wenn ein User keine
            Antwort gibt, gib \"-\" aus.

            Dein Antwortformat ist ausschließlich JSON und muss genau der folgenden Struktur entsprechen,
            um direkt von unserem Code geparst zu werden.

            JSON-Antwortstruktur:
            {
                \"user_0\": \"gewählte_option\",
                \"user_1\": \"gewählte_option\",
                ...
            }

            Ersetze \"gewählte_option\" mit der Option, die der Benutzer gewählt hat. Gib **nur** die JSON-Struktur als
            Antwort aus.
        ";
    }


    /**
     * @param int $userAmount
     * @param int $amountOptions
     *
     * @return string
     * @throws Exception
     */
    public static function getSystemPrompt(int $userAmount = 10, int $amountOptions = 4): string{
        $wantedOutputStructure = [];
        for($i = 0; $i < $userAmount; $i++){
            $wantedOutputStructure["user_" . $i] = "Option_" . random_int(1, $amountOptions);
        }
        $wantedOutputStructureStr = json_encode($wantedOutputStructure, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT);

        return "
            Du bist ein Twitch-Bot, der die Ergebnisse einer Umfrage analysiert. Dir wird eine Frage und die Liste möglicher
            Antwortoptionen gegeben. Analysiere die Antworten der User und bestimme, welche Antwortoption jeder User gewählt hat.
            Beachte, dass die Antworten aus einer Chat-Umgebung stammen und möglicherweise Tippfehler enthalten. Du musst in der
            Lage sein, solche Varianten zu identifizieren und zu interpretieren. Wenn ein User keine Antwort gibt, gib
            \"-\" aus.

            Dein Antwortformat ist ausschließlich JSON und muss genau der folgenden Struktur entsprechen, um direkt von
            unserem Code geparst zu werden.

            JSON-Antwortstruktur:
            $wantedOutputStructureStr

            Verwende **nur** die JSON-Struktur bei deiner Antwort und stelle sicher, dass jeder User eine der
            bereitgestellten Antwortoptionen als Wert hat.
        ";
    }
}
