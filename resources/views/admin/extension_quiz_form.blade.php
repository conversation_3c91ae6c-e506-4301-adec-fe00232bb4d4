@extends('admin.layouts.layout')
@section('content')

    @section('custom_css')
        <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/datepicker.min.css">
    @endsection

    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">Streamer Quiz</h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route("admin_extension_quizzes") }}">Back</a></li>
                    <li class="breadcrumb-item">{{ @$trivia_id ? 'Update' : 'Create' }}</li>
                </ul>
            </div>
            <div class="page-header-right ms-auto">
                <div class="page-header-right-items">
                    <div class="d-flex d-md-none">
                        <a href="javascript:void(0)" class="page-header-right-close-toggle">
                            <i class="feather-arrow-left me-2"></i>
                            <span>Back</span>
                        </a>
                    </div>
                    <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                        <a href="javascript:void(0);" class="btn btn-primary cc-form-submit-btn">
                            <i class="feather-save me-2"></i>
                            <span>Save</span>
                        </a>
                    </div>
                </div>
                <div class="d-md-none d-flex align-items-center">
                    <a href="javascript:void(0)" class="page-header-right-open-toggle">
                        <i class="feather-align-right fs-20"></i>
                    </a>
                </div>
            </div>
        </div>
        <!-- [ page-header ] end -->

        <!-- [ Main Content ] start -->
        <div class="main-content">
            <form id="kt_extension_quiz_frm" class="form" action="{{ route("admin_extension_quiz_save", ["id" => @$extension_quiz_id ?: ""]) }}" method="POST">
                <div class="row">
                    <div class="col-xl-12">
                        
                        <div class="card stretch stretch-full">
                            <div class="card-body">
                                
                                @if (session('suc_message'))
                                    <div class="alert alert-success">{{session('suc_message')}}</div>
                                @endif

                                @if (session('err_message'))
                                    <div class="alert alert-danger">{{session('err_message')}}</div>
                                @endif

                                <div class="row">
                                    @include('admin.form.text_component',  ['name' => 'quiz_name', 'value' => (old('quiz_name') ?: (@$quiz_name ?: "")), 'label' => "Quiz Day", 'required' => true, 'additionalClass' => 'col-md-4', 'inputCls' => 'datepicker_field'])
                                </div>        
                                
                                <div class="row">
                                    @for($i=1;$i<=1;$i++)
                                    
                                        @include('admin.form.text_component', ['name' => 'question['.$i.'][question_title]', 'value' => (old('question.'.$i.'.question_title') ?: (@$questions[$i-1]->question ?: "")), 'label' => 'Question Title '.$i, 'required' => true, 'additionalClass' => 'col-md-8'])

                                        @include('admin.form.text_component', ['name' => 'question['.$i.'][correct_answer]', 'value' => (old('question.'.$i.'.correct_answer') ?: (@$questions[$i-1]->correct_answer ?: "")), 'label' => 'Correct Answer '.$i, 'required' => true, 'additionalClass' => 'col-md-4'])
                                    @endfor
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                @if(@$extension_quiz_id)
                    <input type="hidden" name="extension_quiz_id" value="{{ $extension_quiz_id }}">
                @endif

                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                <button type="submit" class="d-none cc-form-submit">Submit</button>
            </form>

            @if(@$extension_quiz_id && count($quizFinalResults) > 0)

                @foreach($quizFinalResults as $key => $quizResults)    

                    @php
                    $correct_count = $in_correct_count =  0;
                    foreach($quizResults as $quizResult)  {
                        if($quizResult['is_correct'])
                            $correct_count++;
                        else
                            $in_correct_count++;
                    }
                    @endphp        

                    <div class="card stretch stretch-full">
                        <div class="card-body">
                            
                            <div class="d-flex mb-4 flex-wrap justify-content-between">
                                <h5 class="m-0 mr-auto">{!! (isset($questions[$key]->question)) ? $questions[$key]->question : '' !!}</h5>
                                <span class="d-block text-right">{{ $correct_count }} Correct - {{ $in_correct_count }} Incorrect</span>
                            </div>    
                            <div class="table-responsive cc-view-chat-wrap">
                                <table class="table table-striped w-100"  data-toggle="table">
                                    <thead>
                                        <tr>
                                            <th>UserName</th>
                                            <th>Answer</th>
                                            <th>Is Correct?</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($quizResults as $quizResult)

                                            <tr>
                                                <td>{!! $quizResult['user'] !!}</td>
                                                <td>{!! $quizResult['user_answer'] !!}</td>
                                                <td>{!! ($quizResult['is_correct']) ? 'Yes' : 'No' !!}</td>
                                            </tr>

                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            
                        </div>
                    </div>

                @endforeach    
            @endif

            <br><br><br><br><br><br><br><br>
        </div>
        <!-- [ Main Content ] end -->
    </div>

    @section('custom_javascript')
        <script src="{{ asset('public/assets') }}/js/tinymce/tinymce.bundle.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/datepicker.min.js"></script>
    @endsection

@endsection