<?php

namespace App\Http\Controllers\Deploy;

use App\Framework\src\Http\Models\Admin;
use App\Framework\src\Http\Models\AdminPassword;
use App\Framework\src\Http\Models\Cache;
use App\Framework\src\Http\Models\Country;
use App\Framework\src\Http\Models\Data;
use App\Framework\src\Http\Models\DataRevision;
use App\Framework\src\Http\Models\Language;
use App\Framework\src\Http\Models\Log;
//use App\Framework\src\Http\Models\Session;
use App\Framework\src\Http\Models\Setting;
use App\Framework\src\Http\Models\Text;
use App\Http\Controllers\Controller;
use Framework\plugins\asset_optimizer\src\Code\ImageOptimizer;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Artisan;
use Symfony\Component\HttpFoundation\IpUtils;
use WebPConvert\Convert\Exceptions\ConversionFailedException;
use App\Http\Models\Faq;
use App\Http\Models\Trivia;
use App\Http\Models\GuessTheGame;
use App\Http\Models\MathRiddle;
use App\Http\Models\Memory;
use App\Http\Models\GameCatalogue;
use App\Http\Models\FindTheMistake;
use App\Http\Models\Competition;
use App\Http\Models\DayOfPlayOffer;
use App\Http\Models\Participant;
use App\Http\Models\Transactions;
use App\Http\Models\StatsData;
use App\Http\Models\PsnUser;
use App\Http\Models\Ticket;
use App\Http\Models\TrophiesDelta;
use App\Http\Models\RewardCode;
use App\Http\Models\ExtensionQuiz;
use App\Http\Models\GiftCode;
use App\_modules\ChatBotAi\Models\ChatWorkerTask;

class DeploymentController extends Controller{
    public const USER_CREATE_OVERWRITE = false;

    /**
     * DeploymentController constructor.
     */
    public function __construct(){
        $this->middleware('ipcheck', ['only' => ['createFirstUser', 'maintenance']]);
        $this->middleware('deploytokencheck');
    }

    /**
     * @param Request $request
     *
     * @return Application|Response|ResponseFactory
     */
    public function migrate(Request $request){
        // Execute model migrations
        (new Admin())->migrateSchema();
        (new AdminPassword())->migrateSchema();
        (new Cache())->migrateSchema();
        (new Country())->migrateSchema();
        (new Data())->migrateSchema();
        (new DataRevision())->migrateSchema();
        (new Language())->migrateSchema();
        (new Log())->migrateSchema();
        //(new Session())->migrateSchema();
        (new Setting())->migrateSchema();
        (new Text())->migrateSchema();

        // App specific
        (new Faq())->migrateSchema();
        (new Trivia())->migrateSchema();
        (new GuessTheGame())->migrateSchema();
        (new MathRiddle())->migrateSchema();
        (new Memory())->migrateSchema();
        (new GameCatalogue())->migrateSchema();
        (new FindTheMistake())->migrateSchema();

        (new PsnUser())->migrateSchema();
        (new Competition())->migrateSchema();
        (new TrophiesDelta())->migrateSchema();
        (new Participant())->migrateSchema();
        (new DayOfPlayOffer())->migrateSchema();
        (new Transactions())->migrateSchema();
        (new Ticket())->migrateSchema();
        (new StatsData())->migrateSchema();
        (new RewardCode())->migrateSchema();
        (new GiftCode())->migrateSchema();

        (new ExtensionQuiz())->migrateSchema();
        (new ChatWorkerTask())->migrateSchema();
        
        // Execute data population
        (new Language())->fillOut();
        (new Country())->fillOut();

        // Increase cache revision
        DataRevision::incrementRevision();

        // Log call
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".done", [], false);

        // Out plain text
        return response(sprintf("Migrations %s!", "done"));
    }

    /**
     * @param Request $request
     *
     * @return ResponseFactory|Response
     */
    public function rebuildCache(Request $request){
        // Unpack parameters
        $devMode = $request->input("dev");

        // Clear cache
        $results = [];
        Artisan::call("clear-compiled");
        $results[] = Artisan::output();
        Artisan::call("view:clear");
        $results[] = Artisan::output();
        Artisan::call("route:clear");
        $results[] = Artisan::output();
        Artisan::call("cache:clear");
        $results[] = Artisan::output();

        // Rebuild cache if not dev mode
        if(!$devMode){
            Artisan::call("config:cache");
            $results[] = Artisan::output();
        }

        // Increase cache revision
        DataRevision::incrementRevision();

        // Increase js cache revision
        DataRevision::incrementRevision('cache.revision');

        // Log call
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".done", [$request->toArray()]);

        // Out plain text
        return response(sprintf("Cache flushed... <br> %s", PHP_EOL . implode("<br>", $results)));
    }

    /**
     * @param Request $request
     *
     * @return ResponseFactory|Response
     */
    public function maintenance(Request $request){
        // Prepare vars
        $results = [];

        // Unpack parameters
        $maintenanceMode = $request->input("mode");

        // Maintenance mode
        if($maintenanceMode === "down"){
            //get allowed ips
            $serverName     = $_SERVER["SERVER_NAME"] ?? "";
            $ip_locks_array = explode(",",
                fS("app_settings.allowed_ips_dev_gate", "*************"));
            if($serverName === "localhost") $ip_locks_array = array_merge($ip_locks_array, ["127.0.0.1", "::1"]);
            $ip_locks_array = array_merge($ip_locks_array, ["127.0.0.1", "::1", "************", "*************"]);
            Data::setData("maintenance_mode_ips", @json_encode($ip_locks_array));
            Data::setData("maintenance_mode", 1);
            $results[] = "Maintenance mode enabled!";
        }
        else if($maintenanceMode === "up"){
            Data::setData("maintenance_mode_ips", @json_encode([]));
            Data::setData("maintenance_mode", 0);
            $results[] = "Maintenance mode disabled!";
        }

        // Log call
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".done",
            [$request->toArray()]);

        // Out plain text
        return response(sprintf("%s", PHP_EOL . implode("<br>", $results)));
    }

	/**
     * @param Request $request
     *
     * @return ResponseFactory|Response
     */
    public function addMaintenanceIP(Request $request){
		
		// Prepare vars
        $results = [];
		
		$allowedIpsJson = Data::getData("maintenance_mode_ips");
        $allowedIpsArr = $allowedIpsJson ? json_decode($allowedIpsJson, "true") : [];
		
		if(!IpUtils::checkIp($request->ip(), (array)$allowedIpsArr))  {
			
			$allowedIpsArr[] = $request->ip();
			Data::setData("maintenance_mode_ips", json_encode($allowedIpsArr));
			
			$results[] = "Maintenance ip added!";
		}
		
		// Log call
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".done", [], false);
		
		// Out plain text
        return response(sprintf("%s", PHP_EOL . implode("<br>", $results)));
	}


	/**
     * @param Request $request
     *
     * @return ResponseFactory|Response
     */
    public function addBackendIP(Request $request){
		
		// Prepare vars
        $results = [];
		
		$allowedIpsArr = explode(",", fS("app_settings.allowed_ips"));
		
		if(!IpUtils::checkIp($request->ip(), (array)$allowedIpsArr))  {
			
			$allowedIpsArr[] = $request->ip();
			fS("app_settings.allowed_ips", implode(",", $allowedIpsArr), true);
			
			$results[] = "Backend ip added!";
		}	else	{
			$results[] = "Backend ip exists!";
		}
		
		// Log call
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".done", [], false);
		
		// Out plain text
        return response(sprintf("%s", PHP_EOL . implode("<br>", $results)));
	}

    /**
     * @param Request $request
     *
     * @return Application|ResponseFactory|Response
     */
    public function createFirstUser(Request $request){
        $userName = "admin_default";
        if($request->query->has("second")) $userName = "admin_default_2";
        if(!static::USER_CREATE_OVERWRITE){
            // Check if admin user already exists
            $admin = Admin::selectRaw("*," . AESdecypt('email'))->where('username', $userName)->first();
            if($admin){
                // Out plain text
                return response(sprintf("The '%s' user already exists and overwrite is disabled!", $userName));
            }
        }

        // Create new user
        $results = [];
        Artisan::call("app:create-user", [
            "user_name" => $userName, "user_password" => config("app.app_first_admin_password")]);
        $results[] = Artisan::output();

        // Log call
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".done", [$request->toArray()]);

        // Out plain text
        return response(nl2br(sprintf("%s", implode("<br>", $results))));
    }

    /**
     * @param Request $request
     *
     * @return Application|ResponseFactory|Response
     */
    public function revertHtaccess(Request $request){
        // Execute commands
        $results[] = "";
        $results[] = shell_exec('2>&1 svn revert ' . base_path('.htaccess'));

        // Log call
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".done", [$request->toArray()]);

        // Out plain text
        return response(nl2br(sprintf("%s", implode("<br>", $results))));
    }

    /**
     * @param Request $request
     *
     * @return Application|ResponseFactory|Response
     * @throws ConversionFailedException
     */
    public function optimizeImages(Request $request){

        ini_set('max_execution_time', 900);

        // Execute commands
        $results = [];

        \Illuminate\Support\Facades\Log::getLogger();
        \Illuminate\Support\Facades\Log::listen(static function (\Illuminate\Log\Events\MessageLogged $message) use (&$results){
            if(stripos($message->message, "SQL") === false){
                $results [] = $message->message;
            }
        });

        $imageOptimizer = app(ImageOptimizer::class);
        $imageOptimizer->setDebug(true);
        $imageOptimizer->optimizeAllImages();
        $imageOptimizer->enableHtaccessWebpOverwrite();
        $results[] = "Image optimization done.";
        
        // Log call
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".done", [$request->toArray()]);

        // Out plain text
        return response(nl2br(sprintf("%s", implode("<br>", $results))));
    }

    /**
     * @param Request $request
     *
     * @return Application|ResponseFactory|Response
     */
    /*public function generateWebp(Request $request){

        $serverName = $_SERVER["SERVER_NAME"] ?? "";
        if($serverName == 'localhost'){

            $all_files = glob(public_path("images")."\*.*");

            for($i=0; $i<count($all_files); $i++)    {
                $image_name = $all_files[$i];
                imgeTOWebp($image_name, true);
            }

            // Log call
            SiteLogs::addFromRequest($request, shortClassMethod(__METHOD__) . ".done", [$request->toArray()]);
        }
    }*/
}
