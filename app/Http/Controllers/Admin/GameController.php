<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Framework\src\Http\Models\DataRevision;
use App\Framework\src\Http\Models\Log;
use App\Http\Models\GameCatalogue;
use App\Http\Requests\GameSaveRequest;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Models\Points;
use App\Excel\ProductsImport;

class GameController extends Controller{

    /**
     * GameController constructor.
     *
     * @param Request $request
     */
    public function __construct(Request $request){
        $this->middleware('ipcheck');
        $this->middleware('logincheck:game');
    }


    /**
     * @param Request $request
     *
     * @return Factory|View
     */
    public function index(Request $request){

        $data['active_menu'] = 'game';
        return view('admin.games', $data);
    }


    /**
     * @param Request $request
     *
     * @return JSON
     */
    public function ajaxList(Request $request){

        $draw   = intval($_GET["draw"]);
        $start  = intval($_GET["start"]);
        $length = intval($_GET["length"]);
        $search = $_GET["search"];
        $order  = $_GET["order"];

        $search_where = "";
        if(isset($search['value']) && $search['value'] != ""){
            $search_val   = sanitizeStr($search['value']);
            $search_val_e = DB::connection()->getPdo()->quote('%' . $search_val . '%');
            $search_where = "(title LIKE $search_val_e)";
        }

        //get count
        $games     = $search_where ? GameCatalogue::whereRaw($search_where)->get() : GameCatalogue::get();
        $total_count = $games->count();

        //get order by
        $sort_cols = array( 1 => "game_name", 2 => "game_id", 3 => "price");
        if(in_array($order[0]['column'], array_keys($sort_cols))){
            $sort_col   = $order[0]['column'];
            $sort_order = $order[0]['dir'];
        }

        //get games
        $games = GameCatalogue::orderBy($sort_cols[$sort_col], $sort_order)->take($length)->skip($start);
        if($search_where){
            $games = $games->whereRaw($search_where);
        }
        $games = $games->get();


        //table data
        $data = array();
        
        foreach($games as $game){

            $actionData = '<div class="hstack gap-2 justify-content-end">';  // class="text-end"
            $actionData .= '<a data-record-id="'.$game->id.'" class="btn btn-danger" data-kt-record-table-filter="delete_row"><i class="feather-trash-2 me-2"></i><span>Delete</span></a>';
            $actionData .= '</div>';

            $data[] = array(
                '<div class="item-checkbox ms-1">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input checkbox del-check-input" id="checkBox_'.$game->id.'" value="'.$game->id.'">
                        <label class="custom-control-label" for="checkBox_'.$game->id.'"></label>
                    </div>
                </div>',
                $game->game_name,
                $game->game_id,
                "€ ".$game->price,
                $actionData,
            );
        }
		
        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $total_count,
            "recordsFiltered" => $total_count,
            "data"            => $data
        );
        echo json_encode($output);
    }


    /**
     * @param Request $request
     * @param         $id
     *
     * @return Factory|View
     */
    public function edit(Request $request, $id = false){

        /* @var Game $game */
        $game = GameCatalogue::where('id', $id)->first();

        if($game) {

            $data["game_id"]      = $game->id;
			$data["game_name"]          = $game->game_name;
            $data["game_game_id"]          = $game->game_id;
              
        }

        $data['active_menu'] = 'game';
        return view('admin.game_form', $data);
    }


    /**
     * @param GameSaveRequest $request
     * @param int               $id
     *
     * @return RedirectResponse
     */
    public function save(GameSaveRequest $request, $id = false){

        $game_xls = sanitizeStr($request->input("game_xls"));
        
        //Import monthly report
        $tmpFileFullPath = public_path('uploads/tmp') . DIRECTORY_SEPARATOR . $game_xls;
        $excelObj = new ProductsImport();
        Excel::import($excelObj, $tmpFileFullPath);

        Log::addFromRequest($request, shortClassMethod(__METHOD__));
        return redirect()->route("admin_games");
    }


    /**
     * @param Request $request
     *
     * @return RedirectResponse
     * @throws Exception
     */
    public function remove(Request $request){

        $id = (int) sanitizeStr($request->input("record_id"));
        
        if($id > 0)  {
            $record_ids = $id;
        }
        else  {
            $record_ids = sanitizeStr($request->post('record_ids'));
        }
		
        //delete multiple rows
        if($record_ids)  {

            $recordIds = explode(",", $record_ids);

            $games = GameCatalogue :: whereIn('id', $recordIds)->get();
            
            foreach($games as $game)  {
                
                deleteUploadFile($game->memory_image);
                deleteUploadFile($game->guess_the_game_image);
                deleteUploadFile($game->guess_the_game_mobile_image);
                $game->delete();
            }

            DataRevision::incrementRevision();
            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".destroy", ["id" => $record_ids]);      
        }

        return '1';

    }

    public function deleteAll(Request $request){

        $games = GameCatalogue :: all();
            
        foreach($games as $game)  {
            
            deleteUploadFile($game->memory_image);
            deleteUploadFile($game->guess_the_game_image);
            deleteUploadFile($game->guess_the_game_mobile_image);
            $game->delete();
        }

        DataRevision::incrementRevision();
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".destroy"); 

        return redirect()->route("admin_games");
    }
}