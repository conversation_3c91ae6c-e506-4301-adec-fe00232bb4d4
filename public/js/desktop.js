var enoughSlider = null,
    tooltipList = null;
	TwitchPlayer= null,
    last_detail_page = null,
	Timertl = gsap.timeline(),
	akSidebar = '',
    logHistory = true;

gsap.ticker.lagSmoothing(false);

jQuery(document).ready(function($) {

	_body = jQuery('body');
	_html = jQuery('html');
	_htmlBody = jQuery('html, body');
	_window = jQuery(window);
	_layout = jQuery('.layout');


	// default script
	jQuery('#icon_vgfx').load(SITE_URL+'/public/gfx/vgfx.svg');

	// For FAQ
	jQuery('.cc-accordion-body.show').each(function () {
		jQuery(this).css('display', 'block');
	});

	jQuery(document).on('click', '.cc-accordion-handle', function() {
		var ele = jQuery(this),
			eleParent = ele.parents('.cc-accordion:first'),
			eleParents = ele.parents('.cc-accordion-wrapper:first'),
			eleWas = eleParents.find('.cc-accordion-handle[aria-expanded="true"]'),
			bodyWas = eleParents.find('.cc-accordion-body.show');

		if(ele.attr('aria-expanded') == 'true') { // close this
			ele.attr('aria-expanded', false);
			eleParent.find('.cc-accordion-body').stop(true, true).slideUp().removeClass('show');
		} else { // open this and close other
			ele.attr('aria-expanded', true);
			eleParent.find('.cc-accordion-body').stop(true, true).slideDown().addClass('show');
			eleWas.attr('aria-expanded', false);
			bodyWas.stop(true, true).slideUp().removeClass('show');

			var str = '',
			accordionPreText = ele.attr('data-text-pre'),
			accordionVideo = ele.attr('data-video'),
			accordionPostText = ele.attr('data-text-post');

			if( typeof(accordionVideo) != "undefined" && accordionVideo != "" )  {
				str += '<span class="cc-c-text-pre">'+ accordionPreText +'</span>';

				str += '<div class="cc-c-video">'+
					'<video src="'+ accordionVideo +'" autoplay muted loop></video>'+
				'</div>';

				str += '<span class="cc-c-text-post">'+ accordionPostText +'</span>';
				jQuery('.cc-c-gfx').html(str);
			}
		}
	});


	jQuery(document).on('click', '.page-change-nav', function(e) {

        e.preventDefault();

		var linkObj = jQuery(this);
		var next_page = linkObj.attr('data-href');
		var ajax_url = linkObj.attr('href');
		var reload_page = linkObj.attr('data-reload');
        var back_history = linkObj.attr('data-history');
		var action = (linkObj.attr('data-action') != undefined && linkObj.attr('data-action') == 'back') ? 'back' : 'next';
		var existingHTML = jQuery.trim(jQuery("#cc-"+next_page).html());

        if(back_history == "No")  {
            action = "no-history";
        }

		if(reload_page != "yes" && (typeof(ajax_url) == "undefined" || existingHTML != ""))  {

			jQuery('.page-change-nav:not([data-bs-toggle])').removeClass('active');

			page_change(next_page, action);
		}
		else	{

			_html.addClass('loading');
			cc_ajax(ajax_url, {}, function(data) {

				ajaxImagesPreload("#cc-"+next_page, function() {

					_html.removeClass('loading');
					page_change(next_page, action);
				});
			});
		}
	});

    if(jQuery("#cc-memory").length > 0 && jQuery("#cc-memory").hasClass('show'))  {

        setTimeout(function() {
	        cc_adv_game('#cc-memory');
        }, 100);
    }

	//ANIMATION START
	gsap.registerPlugin(ScrollTrigger);

	ScrollTrigger.defaults({
		once: true
	});

	jQuery('.offcanvas').on('show.bs.offcanvas', function (e) {
		_html.addClass('overflow-clip');
	});

	jQuery('.offcanvas').on('hide.bs.offcanvas', function (e) {
		_html.removeClass('overflow-clip');

        if(logHistory)  {
            page_change("dashboard", "only-history");
        }
        else  {
            page_change("dashboard", "no-history-or-change");
            logHistory = true;
        }
	});

    //ps footer show
	jQuery(document).on('click', '.psfooter-showlink', function(e) {
		e.preventDefault();

		_html.addClass('psfooter-show');
		jQuery('.ps-footer').addClass('show').attr({'aria-modal': 'true', 'role': 'dialog'});

	});

	//ps footer close
	jQuery(document).on('click', '.ps-footer-bg, .ps-footer-close', function(e) {
		e.preventDefault();
		_html.removeClass('psfooter-show');
		jQuery('.ps-footer').removeClass('show').removeAttr('aria-modal role');
	});

	initSlider();
	initDashboard();
});

function page_change(next_page, callback)  {

    var nextPageParts = next_page.split("/");
    var selector = nextPageParts[0];

    if(callback != "no-history" && callback != "no-history-or-change")  {

        var title = selector;
        history.pushState({path: next_page, callback: callback, is_mobile: false}, title, SITE_URL + '/' + next_page);
    }

    if(callback != "only-history" && callback != "no-history-or-change")  {
        page_change_html(next_page, callback);
    }
}

function page_change_html(next_page, callback) { // param: next_page (class with url) and action ( next or back )

    var nextPageParts = next_page.split("/");
    var selector = nextPageParts[0];
	var newPageID = "#cc-" + selector;

    if(next_page == "dashboard")  {

        if(jQuery(".offcanvas.show").length > 0)  {

            logHistory = false;
            jQuery(".offcanvas.show").offcanvas('hide');
        }

        _html.removeClass('overflow-hidden');
    }
    else  {

        reset_page(next_page);
        jQuery(newPageID).offcanvas('show');

        //check if there is html, if not then make ajax call
        if(jQuery.trim(jQuery(newPageID).html()) == "")  {

            jQuery("#history-back-page-url").attr('href', SITE_URL+ '/' + next_page);
            jQuery("#history-back-page-url").attr('data-href', selector);
            jQuery("a#history-back-page-url").trigger('click');
            return false;
        }
    }

    track_adobe_page(selector, next_page);

    _window.trigger('lazyload');

    if(next_page == "memory")  {
        cc_adv_game('#cc-memory');
    }

    if(typeof callback == 'function') {
        callback();
    }
}

function initSlider() {

    if(jQuery('.winner-card-swiper').length)  {
        winnerCard = new Swiper('.winner-card-swiper', {
            spaceBetween: 0,
            slidesPerView: 'auto',
            grabCursor:true,
            pagination: {
            clickable: true,
            },
                navigation: {
                    prevEl: '.winner-card-prev',
                    nextEl: '.winner-card-next ',
            },
            speed: 600
        });
    }
}

function initDashboard() {

	if(jQuery('.cc-main-prize').length) {

		var heroTl = gsap.timeline({
			scrollTrigger: {
				trigger: '.cc-main-prize',
				start: 'top 90%'
			}
		});

		heroTl.fromTo('.cc-main-prize-heading-row',
			{ y: 60, opacity: 0 },
			{ duration: 0.5, opacity: 1, y: 0 }
		);

		heroTl.fromTo('.cc-controller-card',
			{ y: 60, opacity: 0 },
			{ duration: 0.5, opacity: 1, y: 0 }, 'main'
		);

		heroTl.fromTo('.guess-card-box',
			{ y: 60, opacity: 0 },
			{ duration: 0.5, stagger: 0.15, opacity: 1, y: 0 }, 'main'
		);

        if(jQuery('.winner-card-wrapper').length) {

            heroTl.fromTo('.winner-card-wrapper > .text-a > *',
                { y: 60, opacity: 0 },
                { duration: 0.5, opacity: 1, stagger: 0.15, y: 0 }, 'stream'
            );

            heroTl.fromTo('.winner-card',
                { y: 60, opacity: 0 },
                { duration: 0.5, opacity: 1, stagger: 0.15, y: 0 }, 'stream'
            );
        }
	}
}
