-- SQL statements to create indexes for optimizing query performance
-- Generated based on analysis of all models and their query patterns

-- psn_users table
-- user_id is frequently used in WHERE clauses in related models
CREATE INDEX IF NOT EXISTS idx_psn_users_id ON psn_users(id);
-- Index for date range queries on user creation
CREATE INDEX IF NOT EXISTS idx_psn_users_created_at ON psn_users(created_at);
-- Index for country filtering in winner queries
CREATE INDEX IF NOT EXISTS idx_psn_users_country_code_deleted_at ON psn_users(country_code, deleted_at);
-- These fields already have indexes defined in the model
-- online_id, user_uuid, country_code, nickname, fake_user, deleted_at

-- tickets table
-- user_id is frequently used in WHERE clauses but doesn't have an index
CREATE INDEX IF NOT EXISTS idx_tickets_user_id ON tickets(user_id);
-- Combined index for common query patterns in PsnUser model
CREATE INDEX IF NOT EXISTS idx_tickets_user_id_identifier ON tickets(user_id, identifier);
-- Combined index for unread tickets count query
CREATE INDEX IF NOT EXISTS idx_tickets_user_id_is_view ON tickets(user_id, is_view);
-- Index for timestamp filtering
CREATE INDEX IF NOT EXISTS idx_tickets_created_at ON tickets(created_at);
-- These fields already have indexes defined in the model
-- ticket, identifier, type, is_view

-- participants table
-- user_id and competition_id are frequently used in WHERE clauses but don't have indexes
CREATE INDEX IF NOT EXISTS idx_participants_user_id ON participants(user_id);
CREATE INDEX IF NOT EXISTS idx_participants_competition_id ON participants(competition_id);
-- is_winner is frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_participants_is_winner ON participants(is_winner);
-- Combined indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_participants_competition_id_is_winner ON participants(competition_id, is_winner);
CREATE INDEX IF NOT EXISTS idx_participants_competition_id_is_winner_prize_type ON participants(competition_id, is_winner, prize_type);
CREATE INDEX IF NOT EXISTS idx_participants_competition_id_is_winner_tickets ON participants(competition_id, is_winner, tickets);
CREATE INDEX IF NOT EXISTS idx_participants_day_is_winner_tickets ON participants(day, is_winner, tickets);
CREATE INDEX IF NOT EXISTS idx_participants_is_winner_updated_at_user_id ON participants(is_winner, updated_at, user_id);
-- Index for timestamp filtering and winner queries with country filtering
CREATE INDEX IF NOT EXISTS idx_participants_updated_at ON participants(updated_at);
CREATE INDEX IF NOT EXISTS idx_participants_day_is_winner ON participants(day, is_winner);
-- These fields already have indexes defined in the model
-- tickets

-- competitions table
-- date is frequently used in WHERE clauses but doesn't have an index
CREATE INDEX IF NOT EXISTS idx_competitions_date ON competitions(date);
-- These fields already have indexes defined in the model
-- code

-- memories table
-- These fields already have indexes defined in the model
-- day, code

-- trivia table
-- day is frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_trivia_day ON trivia(day);

-- guess_the_games table
-- day is frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_guess_the_games_day ON guess_the_games(day);

-- math_riddles table
-- day is frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_math_riddles_day ON math_riddles(day);

-- find_the_mistakes table
-- day is frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_find_the_mistakes_day ON find_the_mistakes(day);

-- day_of_play_offers table
-- sort_order is frequently used in ORDER BY clauses
CREATE INDEX IF NOT EXISTS idx_day_of_play_offers_sort_order ON day_of_play_offers(sort_order);

-- faqs table
-- is_display and sort_order are frequently used in WHERE and ORDER BY clauses
CREATE INDEX IF NOT EXISTS idx_faqs_is_display ON faqs(is_display);
CREATE INDEX IF NOT EXISTS idx_faqs_sort_order ON faqs(sort_order);
-- Covering index for common query pattern
CREATE INDEX IF NOT EXISTS idx_faqs_is_display_sort_order_question_answer ON faqs(is_display, sort_order, question(255), answer(255));

-- app_stats table
-- key and day are frequently used in WHERE clauses
CREATE INDEX IF NOT EXISTS idx_app_stats_key_day ON app_stats(key, day);
-- Covering index for common query pattern
CREATE INDEX IF NOT EXISTS idx_app_stats_key_day_value ON app_stats(key, day, value(255));

-- trophies_delta table
-- user_id and day are frequently used in WHERE clauses and JOINs
CREATE INDEX IF NOT EXISTS idx_trophies_delta_user_id_day ON trophies_delta(user_id, day);
-- Index for queries filtering by day IS NULL (first login trophies)
CREATE INDEX IF NOT EXISTS idx_trophies_delta_day_null ON trophies_delta(day);
-- Index for queries filtering by tickets > 0
CREATE INDEX IF NOT EXISTS idx_trophies_delta_tickets ON trophies_delta(tickets);
-- Combined index for common aggregation queries
CREATE INDEX IF NOT EXISTS idx_trophies_delta_day_tickets ON trophies_delta(day, tickets);
-- These fields already have indexes defined in the model
-- user_id, day

-- reward_codes table
-- psn_user_id is frequently used but doesn't have an index
CREATE INDEX IF NOT EXISTS idx_reward_codes_psn_user_id ON reward_codes(psn_user_id);
-- Combined index for available code queries
CREATE INDEX IF NOT EXISTS idx_reward_codes_reward_id_status ON reward_codes(reward_id, status);
-- Index for sync operations
CREATE INDEX IF NOT EXISTS idx_reward_codes_is_sync_status ON reward_codes(is_sync, status);
-- Index for given_time filtering
CREATE INDEX IF NOT EXISTS idx_reward_codes_given_time ON reward_codes(given_time);
-- These fields already have indexes defined in the model
-- reward_id, code, status, is_sync

-- gift_codes table
-- used field is frequently queried but doesn't have an index
CREATE INDEX IF NOT EXISTS idx_gift_codes_used ON gift_codes(used);
-- Combined index for available codes
CREATE INDEX IF NOT EXISTS idx_gift_codes_code_used ON gift_codes(code, used);
-- These fields already have indexes defined in the model
-- code

-- game_catalogues table
-- is_sync is frequently used for filtering
-- These fields already have indexes defined in the model
-- game_id, code, is_sync

-- extension_quizzes table
-- quiz_name is frequently used for grouping and filtering
CREATE INDEX IF NOT EXISTS idx_extension_quizzes_quiz_name_quiz_code ON extension_quizzes(quiz_name, quiz_code);
-- Index for time-based filtering
CREATE INDEX IF NOT EXISTS idx_extension_quizzes_quiz_start_time ON extension_quizzes(quiz_start_time);
CREATE INDEX IF NOT EXISTS idx_extension_quizzes_quiz_end_time ON extension_quizzes(quiz_end_time);
-- These fields already have indexes defined in the model
-- quiz_name, quiz_code

-- chat_worker_tasks table (ChatBot module)
-- Combined index for task processing queries
CREATE INDEX IF NOT EXISTS idx_chat_worker_tasks_type_done ON chat_worker_tasks(type, done);
-- Index for group-based task queries
CREATE INDEX IF NOT EXISTS idx_chat_worker_tasks_group_key_done ON chat_worker_tasks(group_key, done);
-- Combined index for completed task queries
CREATE INDEX IF NOT EXISTS idx_chat_worker_tasks_type_group_done ON chat_worker_tasks(type, group_key, done);
-- Index for task cleanup and monitoring
CREATE INDEX IF NOT EXISTS idx_chat_worker_tasks_created_at ON chat_worker_tasks(created_at);
-- These fields already have indexes defined in the model
-- type, group_key, done

-- Framework models indexes
-- texts table (Framework)
-- Combined index for language-specific text retrieval
CREATE INDEX IF NOT EXISTS idx_texts_language_id_key ON texts(language_id, key);
-- These fields already have indexes defined in the model
-- language_id, key

-- sessions table (Framework)
-- Index for session cleanup and user session queries
CREATE INDEX IF NOT EXISTS idx_sessions_user_id_last_activity ON sessions(user_id, last_activity);
CREATE INDEX IF NOT EXISTS idx_sessions_last_activity ON sessions(last_activity);
-- Index for IP-based queries
CREATE INDEX IF NOT EXISTS idx_sessions_ip_address ON sessions(ip_address);

-- languages table (Framework)
-- These fields already have indexes defined in the model
-- code, lang_code

-- admins table (Framework)
-- Index for user type filtering
CREATE INDEX IF NOT EXISTS idx_admins_user_type ON admins(user_type);
-- Index for password expiration
CREATE INDEX IF NOT EXISTS idx_admins_pass_expire ON admins(pass_expire);

-- admin_passwords table (Framework)
-- Index for admin password history lookups
CREATE INDEX IF NOT EXISTS idx_admin_passwords_admin_id ON admin_passwords(admin_id);
-- Index for password creation date filtering
CREATE INDEX IF NOT EXISTS idx_admin_passwords_created_at ON admin_passwords(created_at);
-- Combined index for admin password queries
CREATE INDEX IF NOT EXISTS idx_admin_passwords_admin_id_created_at ON admin_passwords(admin_id, created_at);

-- cache table (Framework)
-- Index for cache expiration cleanup
CREATE INDEX IF NOT EXISTS idx_cache_expiration ON cache(expiration);
-- Combined index for cache key lookups with expiration
CREATE INDEX IF NOT EXISTS idx_cache_key_expiration ON cache(key, expiration);
-- These fields already have indexes defined in the model
-- key (unique)

-- Indexes for foreign key relationships
-- These help with JOIN operations
CREATE INDEX IF NOT EXISTS idx_tickets_transaction_id ON tickets(transaction_id);
CREATE INDEX IF NOT EXISTS idx_participants_user_id_competition_id ON participants(user_id, competition_id);

-- Additional performance optimization indexes
-- These indexes target specific query patterns found in the codebase

-- Covering indexes for frequently accessed data
-- Participants with user details (for winner displays)
CREATE INDEX IF NOT EXISTS idx_participants_covering_winners ON participants(is_winner, updated_at, user_id, competition_id, prize_type);

-- Stats data covering index for key-day-value queries
CREATE INDEX IF NOT EXISTS idx_app_stats_covering ON app_stats(key, day, value(100));

-- Composite indexes for complex JOIN queries
-- Participants joined with competitions and users (common in dashboard queries)
CREATE INDEX IF NOT EXISTS idx_participants_competition_user_winner ON participants(competition_id, user_id, is_winner, updated_at);

-- Trophies delta for aggregation queries with user joins
CREATE INDEX IF NOT EXISTS idx_trophies_delta_aggregation ON trophies_delta(user_id, day, tickets, bronze, silver, gold, platinum);

-- IMPORTANT NOTES AND RECOMMENDATIONS:
--
-- 1. EXISTING INDEXES: Some indexes might already exist in the database even if not explicitly
--    defined in the models. Check with: SHOW INDEXES FROM table_name;
--
-- 2. PERFORMANCE TESTING: Before applying in production:
--    - Use EXPLAIN to analyze query performance before and after adding indexes
--    - Test with realistic data volumes
--    - Monitor query execution times
--
-- 3. WRITE PERFORMANCE IMPACT: Indexes improve SELECT performance but can slow down
--    INSERT, UPDATE, and DELETE operations. Consider this for high-write tables.
--
-- 4. INDEX MONITORING: Regularly monitor index usage with:
--    - SHOW INDEX FROM table_name
--    - Performance schema queries to identify unused indexes
--    - Consider dropping unused indexes to improve write performance
--
-- 5. COMPOSITE INDEX ORDER: Column order in composite indexes matters:
--    - Most selective columns should come first
--    - Consider query patterns when ordering columns
--    - Leftmost prefix rule applies
--
-- 6. TEXT FIELD INDEXING: For covering indexes on TEXT fields, we've limited the
--    indexed length to avoid excessively large indexes
--
-- 7. MAINTENANCE: Regularly review and optimize indexes based on:
--    - Query patterns changes
--    - Data growth
--    - Performance monitoring results
--
-- 8. DEPLOYMENT STRATEGY:
--    - Apply indexes during low-traffic periods
--    - Consider creating indexes ONLINE if supported
--    - Monitor system resources during index creation
--
-- 9. SPECIFIC OPTIMIZATIONS INCLUDED:
--    - Date-based filtering for game modules (trivia, memory, etc.)
--    - User-based queries for tickets and participants
--    - Winner selection and display queries
--    - Statistics aggregation queries
--    - Admin panel search and filtering
--    - Foreign key relationship optimizations
--    - ChatBot task processing and queue management
--    - Session management and cleanup
--    - Text localization and language-specific queries
