<script nonce="<?php echo e(csp_nonce()); ?>">
var digitalData = {
    page: {
        pageInfo: {
            pageName: "web:dop<?php echo e(date('Y', time())); ?>:<?php echo e($page_type); ?>",
            destinationURL: "<?php echo e(url()->current()); ?>",
            sysEnv: "<?php echo e(@$is_mobile ? 'mobile' : 'web'); ?>",
            version: "<?php echo e(dev_test_mode() ? 'dev' : 'prod'); ?>",
            onsiteSearchTerm: "",
            onsiteSearchResults: "",
        },
        category: {
            pageType: "<?php echo @$page_type; ?>"
        }
    },
    user: {
        profile: {
            profileInfo: {
                profileID: <?php echo @$psnUser ? '"'.hash('md5', @$psnUser->online_id).'"' : 'null'; ?>,
                language: "de-de"
            },
            attributes: {
                visitorType: "<?php echo @$psnUser ? 'signed-in' : 'guest'; ?>",
                plusUser: "<?php echo @$psnUser && $psnUser->ps_plus_status == 'member' ? 'plususer' : 'nonplususer'; ?>"
            }
        }
    },
    pageInstanceID: "dop<?php echo e(date('Y', time())); ?>:1.0",
};

var adobeTrackOn = false;
function consentAccepted() {

    adobeTrackOn = true;

    if (!document.getElementById('adobe-analytics')) {

        const scriptTag = document.createElement('script');
        scriptTag.setAttribute('id', 'adobe-analytics');
        <?php if(dev_test_mode()): ?>
            scriptTag.setAttribute('src', '//assets.adobedtm.com/3dc13bcabc29/3a3b07f97102/launch-08eeabbe527b-development.min.js');
        <?php else: ?>
            scriptTag.setAttribute('src', '//assets.adobedtm.com/3dc13bcabc29/3a3b07f97102/launch-6dd462263625.min.js');
        <?php endif; ?>
        scriptTag.setAttribute('nonce', '<?php echo e(csp_nonce()); ?>');
        scriptTag.setAttribute('async', '');

        document.head.appendChild(scriptTag);

        //adobe analytics
        var pageloadInterval = setInterval(() => {

            if(typeof(_satellite) != "undefined")  {
                clearInterval(pageloadInterval);
                _satellite.track('pageload');
            }
        }, 1000);
    }
}
</script><?php /**PATH S:\contentcreators\svn\2018\clients\sied\dop-2025\resources\views/site/elements/adobe_digital_data.blade.php ENDPATH**/ ?>