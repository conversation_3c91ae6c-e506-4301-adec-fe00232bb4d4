<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FaqSaveRequest extends FormRequest{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(){
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(){
        $rules = [
            'question'   => 'required',
            'answer'     => 'required',
            'sort_order' => 'required',
        ];

        return $rules;
    }

    /**
     * Custom message for validation
     *
     * @return array
     */
    public function messages(){
        return [
            //'reward_name.required' 	=> 'Reward Name is required!',
        ];
    }
}
