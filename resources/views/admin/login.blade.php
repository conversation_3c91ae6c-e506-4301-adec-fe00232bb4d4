@extends('admin.layouts.auth')
@section('content')

    <div class="auth-cover-sidebar-inner">
        <div class="auth-cover-card-wrapper">
            <div class="auth-cover-card p-sm-5">
                <div class="wd-50 mb-5">
                    <img src="{{ asset('public/assets') }}/images/cc-logo.png" alt="" class="img-fluid">
                </div>
                <h2 class="fs-20 fw-bolder mb-4">{{ config("app.name") }}</h2>
                <p class="fs-12 fw-medium text-muted">Enter your username, password and google authenticator code to login.</p>

                @if (session('password_changed'))
                    <div class="alert alert-success mt-4">Your password has been updated successfully.</div>
                @endif

                @if ($errors->any() || session()->get('err_msg') )
                    
                    <div class="alert alert-danger login-errors mt-4">
                        @if ($errors->any() )
                            @foreach ($errors->all() as $error)
                                {{ $error }}<br>
                            @endforeach
                        @endif

                        @if (session()->get('err_msg'))
                            {{ session()->get('err_msg') }}<br>

                            @php
                            session()->forget('err_msg');
                            @endphp
                        @endif
                    </div>
                @endif

                <form novalidate="novalidate" id="kt_sign_in_form" data-kt-redirect-url="{{ route('admin_validate_login') }}" action="#" autocomplete="off" method="POST" class="w-100 mt-4 pt-2">
                    <div class="mb-4">
                        
                        <input type="text" class="form-control" name="username" id="username" placeholder="Username" autocomplete="off" value="{{ old('username') }}" required>

                    </div>
                    <div class="mb-3">
                        <input type="password" class="form-control" name="password" id="password" placeholder="Password" value="" autocomplete="off" required>

                    </div>
                    <div class="mb-4">
                        <input type="text" class="form-control" placeholder="Google Authenticator Code" name="ga_code" id="ga_code" autocomplete="off" value="{{ old('ga_code') }}" required>

                    </div>
                    <div class="d-flex align-items-center justify-content-end">
                        
                        <div>
                            <a href="{{ route('admin_forgot_password') }}" class="fs-11 text-primary">Forget password?</a>
                        </div>
                    </div>
                    <div class="mt-5">
                        <button type="submit" id="kt_sign_in_submit" class="btn btn-lg btn-primary w-100">Sign In</button>
                    </div>
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                </form>
                <div class="w-100 mt-5 mx-auto">
                    <div class="mb-4 border-bottom position-relative text-center"><span class="small py-1 px-3 text-uppercase text-muted bg-white position-absolute translate-middle">or</span></div>
                    <div class="text-left">
                        <p><strong>Only for registered admins</strong></p>
                    </div>
                    <div class="d-flex align-items-center justify-content-center gap-2">
                        <button type="button" class="btn btn-lg btn-secondary w-100 func-open-google-login-prompt">Google Login</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('custom_javascript')
    <script src="{{ asset('public/assets') }}/js/custom/authentication/sign-in/general.js"></script>
    <script src="https://accounts.google.com/gsi/client" async defer></script>

    <script>
    window.onload = function () {

        google.accounts.id.initialize({
            client_id: "{{ config('services.google.client_id') }}", // Replace with your Google Client ID
            callback: handleCredentialResponse
        });

        delete_cookie('g_state');
        google.accounts.id.prompt(); // Display the One Tap dialog

        jQuery(".func-open-google-login-prompt").click(function()  {

            delete_cookie('g_state');
            google.accounts.id.prompt(); // Display the One Tap dialog
        });

        function handleCredentialResponse(response) {
            validateGoogleLogin(response.credential);
        }
    }
    </script>
@endsection