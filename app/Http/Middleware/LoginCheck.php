<?php

namespace App\Http\Middleware;

use Closure;
use App\Framework\src\Http\Models\Admin;
use Illuminate\Support\Facades\Request;

class LoginCheck{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next, $module_name=false)  {

        if($request->session()->has('isLogined') && $request->session()->get('isLogined') === config('app.name'))  {

            $admin_id = $request->session()->get('backend_user_id');
            $admin    = Admin::selectRaw("*," . AESdecypt('email'))->where('id', $admin_id)->first();

            if($admin)  {
                
                $request->session()->put('backend_user', $admin);
                $handled = Request::is('*change-password*') || Request::is('*change-pass*') || Request::is('*save-pass*');

                if(!$admin->pass_expire && !$handled)
                    return redirect('admin/change-password/first');
                else if(time() > $admin->pass_expire && !$handled)
                    return redirect('admin/change-password/expire');
                else  {

                    $user_roles = explode(",", $admin->user_roles);

                    if($admin->user_type == "admin" || $module_name === false || in_array($module_name, $user_roles))  {
                        return $next($request);
                    }

                    return redirect(route("admin_overview"));
                }
            }
            else  {
                $request->session()->flush();
            }
        }

        return redirect('admin/login');
    }
}
