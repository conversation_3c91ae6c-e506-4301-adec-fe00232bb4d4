<div class="form-group cc-form-field-include {{ $additionalClass ?? "" }}">
    @if(isset($label) && $label)
        <label for="{{ $id ?? $name }}">
            {{ $label }}
        </label>
    @endif
    <input type="password" class="form-control" name="{{ $name }}" id="{{ $id ?? $name }}"
           placeholder="{{ $placeholder ?? "" }}"
           @isset($value) value="{{ $value }}" @endisset @if(isset($readonly) && $readonly) readonly="readonly" @endif
           @if(isset($confirm_password) && $confirm_password) data-validation="confirmation" required @endif
           @if(isset($required) && $required)  data-validation="strength" data-validation-strength="2" @endif
           >
    @isset($info)
        <small id="{{ $id ?? $name . "_info" }}" class="form-text text-muted">
            {{ $info }}
        </small>
    @endisset
    @if ($errors->has($name))
        <span class="error help-block invalid-feedback show_error">
            @foreach($errors->get($name) as $error)
                {{ $error }} <br>
            @endforeach
        </span>
    @endif
</div>
