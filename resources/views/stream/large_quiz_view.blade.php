<!doctype html>
<html lang="de">
<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta name="viewport" content="width=1920, initial-scale=1, shrink-to-fit=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#242424">
    
    <meta name="sitepath" content="{{ url('') }}">
    <meta name="csrf_token" content="{{ csrf_token() }}">

    <link rel="shortcut icon" type="image/x-icon" href="{{ url('public') }}/stream/gfx/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url('public') }}/stream/gfx/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url('public') }}/stream/gfx/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url('public') }}/stream/gfx/favicon-16x16.png">
    
    <link rel="stylesheet" href="{{ url('public') }}/css/font-all.css">
    <style>
    input[type="text"], input[type="email"], input[type="password"],input[type="number"], input[type="tel"], input[type="button"],input[type="reset"],input[type="submit"], button{-webkit-font-smoothing:antialiased;-moz-font-smoothing:antialiased; font-family: "sst", Arial,sans-serif; font-weight: 400; -webkit-appearance: none; }
    html { font-size: 10px; }
    body { font-size: 1.6rem; font-weight: 400; line-height: 1.5; color: #171D2E; position: relative; font-family: "sst", Arial,sans-serif; margin:0;padding:0; background-color: #DDE0E8; overflow: hidden; }

    html,
    body { position: relative; padding: 0; margin: 0; width: 1920px; height: 1080px; overflow: hidden; }

    .d-none { display: none !important; }

    *, *:before, *:after { -webkit-box-sizing: border-box;box-sizing: border-box; }

    a { color: #fff; font-weight: normal; cursor: pointer; -webkit-transition: color 0.15s linear; -o-transition: color 0.15s linear; transition: color 0.15s linear; text-decoration: underline; outline: none; }
    a:hover { color: #fff; text-decoration: none; }
    a:not(:hover):focus { outline: thin dotted; outline-offset: -1px; }
    a.btn,
    a.btn:hover { text-decoration: none; }


    .cc-stream2 { position: relative; z-index: 0; width: 192rem; height: 108rem; }
    .cc-stream2-bg { display: block; width: 100%; height: 100%; -o-object-fit: cover; object-fit: cover; position: absolute; top: 0; left: 0; z-index: 0; }
    .cc-stream2-logo { display: block; width: 97px; height: 51px; -o-object-fit: contain; object-fit: contain; position: absolute; top: 28px; left: 27px; z-index: 20; }
    .cc-stream2-video { position: absolute; top: 97px; right: 24px; z-index: 11; width: 707px; height: 937px; }
    .cc-stream2-footer { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; position: absolute; left: 48px; bottom: 40px; z-index: 10; width: 1100px; font-size: 24px; font-style: normal; font-weight: 900; line-height: 1.333; text-transform: uppercase; }
    .cc-stream2-footer > svg { width: 60px; height: 60px; max-width: 60px; -webkit-box-flex: 0; -ms-flex: 0 0 60px; flex: 0 0 60px; margin-right: 37px; -ms-flex-item-align: start; align-self: flex-start; }


    .cc-stream2-inner { position: absolute; top: 47px; right: 131px; width: 1670px; height: 664px; overflow: hidden; -webkit-transform: scaleY(0); -ms-transform: scaleY(0); transform: scaleY(0); }
    .cc-stream2-q-bg { display: block; width: 100%; height: 100%; -o-object-fit: cover; object-fit: cover; position: absolute; top: 0; left: 0; z-index: 0; }
    .cc-stream2-text-line { display: block; width: 1657px; height: 558px; -o-object-fit: cover; object-fit: cover; position: absolute; top: 84px; left: 8px; z-index: 2; }
    .cc-stream2-q { display: block; width: 168px; height: 168px; -o-object-fit: cover; object-fit: cover; position: absolute; top: 0px; left: calc(50% - 84px); z-index: 3; opacity: 0; -webkit-transform: scale(0); -ms-transform: scale(0); transform: scale(0); }

    .cc-stream2-q-text { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-box-align: start; -ms-flex-align: start; align-items: flex-start; color: #FFF; text-shadow: 0px 0px 7px rgba(255, 255, 255, 0.50); font-size: 100px; font-style: normal; font-weight: 900; line-height: 110%; text-transform: uppercase; width: 1114px; height: 330px; overflow: hidden; position: absolute; top: 215px; left: 100px; z-index: 1; opacity: 0; }
    .cc-stream2-q-text2 { line-height: 110%; }
    .cc-stream2-video-wrapper { position: absolute; top: 0; right: 0; z-index: 11; width: 1920px; height: 1080px; }
    </style>

    <title>Days of play 2025 Competition</title>
    <meta name="description" content="Days of play 2025 Competition">
</head>

<body class="d-none">

    <!-- Start [ s2 ] -->
    <div class="cc-stream2">

        <div class="cc-stream2-video-wrapper"></div>

        {{--
        <div class="cc-stream2-footer">
            <svg xmlns="http://www.w3.org/2000/svg" width="59" height="59" viewBox="0 0 59 59" fill="none">
                <path d="M14.7503 34.4166H34.417V29.5H14.7503V34.4166ZM14.7503 27.0416H44.2503V22.125H14.7503V27.0416ZM14.7503 19.6666H44.2503V14.75H14.7503V19.6666ZM4.91699 54.0833V9.83329C4.91699 8.48121 5.39842 7.32374 6.36126 6.3609C7.32411 5.39805 8.48158 4.91663 9.83366 4.91663H49.167C50.5191 4.91663 51.6765 5.39805 52.6394 6.3609C53.6022 7.32374 54.0837 8.48121 54.0837 9.83329V39.3333C54.0837 40.6854 53.6022 41.8428 52.6394 42.8057C51.6765 43.7685 50.5191 44.25 49.167 44.25H14.7503L4.91699 54.0833ZM12.6607 39.3333H49.167V9.83329H9.83366V42.0989L12.6607 39.3333Z" fill="#171D2E"/>
            </svg>
            <div class="cc-stream2-footer-text">Lorem ipsum dolor sit amet</div>
        </div>
        --}}

    </div>
    <!-- End [ s2 ] -->

    <script src="{{ url('public') }}/js/jquery.min.js" nonce="{{ csp_nonce() }}"></script>
    <script src="{{ url('public') }}/stream/js/gsap.min.js" nonce="{{ csp_nonce() }}"></script>
    <script src="{{ url('public') }}/stream/js/ajax.js" nonce="{{ csp_nonce() }}"></script>
    <script>

        var SITE_URL = jQuery("meta[name=sitepath]").attr('content');

        var isTextSet = false,
        isVideoPlay = false,
        isAnimate = false;

        getQuestionData('large','');

        function getQuestionData(current_screen, extension_code){

            cc_ajax("stream/question-wrap-data", { current_screen : current_screen , extension_code : extension_code }, function(data)  {
            
                if(data.is_update)
                {
                    // for text resize
                    resize_to_fit();

                    var vid = document.getElementById("cc-stream2-video");
                    vid.onplay = function() {
                        isVideoPlay = true;
                        cc_animation();
                    }; 
                }    
            });
        }

        setInterval(function(){ 
            
            isTextSet = false;
            isVideoPlay = false;
            isAnimate = false;
            var extension_code = jQuery('.cc-stream2-video-wrapper').find('input[name=extension_code]').val();
            getQuestionData('large', extension_code);
        }, 4000);

        function resize_to_fit() {

            // for text resize
            const output = document.querySelector('.cc-stream2-q-text2');
            const outputContainer = document.querySelector('.cc-stream2-q-text');

            let fontSize = window.getComputedStyle(output).fontSize;
            output.style.fontSize = (parseFloat(fontSize) - 1) + 'px';
            
            if(output.clientHeight >= outputContainer.clientHeight){
                resize_to_fit();
            } else {
                isTextSet = true;
                cc_animation();
            }
        }

        function cc_animation() {

            if(isTextSet && isVideoPlay && !isAnimate) {

                isAnimate = true;

                var tl = gsap.timeline();
                tl.to('.cc-stream2-inner', { duration: 0.3, scaleY: 1, ease: 'none' });
                tl.to('.cc-stream2-q-text', { duration: 0.3, opacity: 1,  ease: 'none' }, '-=0.05');
                tl.to('.cc-stream2-q', { duration: 0.3, scale: 1, opacity: 1,  ease: 'none' }, '-=0.45');
                // console.log('in');

            }

        }

    </script>

</body>
</html>