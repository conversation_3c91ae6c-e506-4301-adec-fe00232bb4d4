{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2.0", "ext-curl": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-intl": "*", "ext-json": "*", "doctrine/dbal": "^3.6", "geoip2/geoip2": "~2.0", "ghostzero/tmi": "^2.3", "google-gemini-php/client": "^1.0", "google/apiclient": "^2.18", "google/apiclient-services": "^0.396.0", "google/cloud-translate": "^1.20", "guzzlehttp/guzzle": "^7.7", "laravel/framework": "^10.8", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "maatwebsite/excel": "^3.1", "marktopper/doctrine-dbal-timestamp-type": "^1.0", "picqer/php-barcode-generator": "^2.2", "riverskies/laravel-mobile-detect": "^1.3", "rosell-dk/webp-convert": "^2.9", "spatie/image-optimizer": "^1.6", "spatie/laravel-csp": "^2.8", "stolz/assets": "^0.2.0"}, "require-dev": {"laravel/ui": "^4.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "spatie/laravel-ignition": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}, "google/apiclient-services": ["FirebaseCloudMessaging", "Oauth2"]}, "autoload": {"files": ["app/Framework/src/functions.php", "app/Http/helpers.php", "app/Framework/src/Override/cssmin/Minifier.php", "app/Framework/src/Override/jsmin/JSMin.php", "app/Framework/src/Override/assets/Manager.php", "app/Override/CloudWatchServiceProvider.php"], "psr-4": {"App\\": "app/", "Framework\\": "_framework/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "exclude-from-classmap": ["vendor/tubalmartin/cssmin/src/Minifier.php", "vendor/mrclay/jsmin-php/src/JSMin/JSMin.php", "vendor/stolz/assets/src/Manager.php", "vendor/pagevamp/laravel-cloudwatch-logs/src/Providers/CloudWatchServiceProvider.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"pre-autoload-dump": "Google\\Task\\Composer::cleanup", "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}