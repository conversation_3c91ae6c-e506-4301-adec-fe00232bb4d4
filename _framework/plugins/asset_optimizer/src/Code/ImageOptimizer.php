<?php

namespace Framework\plugins\asset_optimizer\src\Code;

use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Spa<PERSON>\ImageOptimizer\OptimizerChain;
use Spa<PERSON>\ImageOptimizer\OptimizerChainFactory;
use Spatie\ImageOptimizer\Optimizers\Cwebp;
use Spatie\ImageOptimizer\Optimizers\Gifsicle;
use Spatie\ImageOptimizer\Optimizers\Jpegoptim;
use Spatie\ImageOptimizer\Optimizers\Optipng;
use Spatie\ImageOptimizer\Optimizers\Pngquant;
use Spatie\ImageOptimizer\Optimizers\Svgo;
use Symfony\Component\Finder\Finder;
use WebPConvert\Convert\Exceptions\ConversionFailedException;
use WebPConvert\WebPConvert;
use WebPConvert\Exceptions\InvalidInput\InvalidImageTypeException;

class ImageOptimizer{
    protected $debug = false;

    /**
     * @param false $debug
     */
    public function __construct(bool $debug = false){
        $this->debug = $debug;
    }

    /**
     * @param bool $debug
     */
    public function setDebug(bool $debug = false): void{
        $this->debug = $debug;
    }

    /**
     * @param bool  $excludeNonDefaultDirs
     * @param array $excludeDirs
     *
     * @return void
     * @throws ConversionFailedException
     */
    public function optimizeAllImages(bool $excludeNonDefaultDirs = true, array $excludeDirs = []): void{
        $excludeDirectories = is_array($excludeDirs) ? $excludeDirs : [];
        $optimizeExtensions = ["png", "jpg", "jpeg"];
        $finder             = Finder::create()->files()->ignoreDotFiles(true)->in(base_path('public'));
        if($excludeNonDefaultDirs){
            $excludeDirectories = array_merge($excludeDirectories, [
                "assets",
                "min",
            ]);
        }
        if($excludeDirectories){
            Log::info("Exclude following subdirectories from image optimization: " . implode(", ", $excludeDirectories));
            $finder = $finder->exclude($excludeDirectories);
        }
        $files = iterator_to_array($finder->sortByName(), false);
        foreach($files as $file){
            if(stripos($file->getFilename(), "_optbak") === false && in_array(strtolower($file->getExtension()), $optimizeExtensions)){
                $this->optimizeImage($file->getPath() . DIRECTORY_SEPARATOR . $file->getFilename());
            }
        }
    }

    /**
     * @param string $path
     *
     * @throws ConversionFailedException
     */
    public function optimizeImage(string $path = ""): void{
        
        $relativePath       = str_replace(base_path(), "", $path);
        $currentFileHash    = hash_file("sha256", $path);
        $pathParts          = pathinfo($path);
        $newFileWoExt       = $pathParts["dirname"] . DIRECTORY_SEPARATOR . $pathParts["filename"];
        $backupPath         = $newFileWoExt . "_optbak." . $pathParts["extension"];
        $relativeBackupPath = str_replace(base_path(), "", $backupPath);
        $afterPath          = $newFileWoExt . "." . $pathParts["extension"];
        $avifPath           = $newFileWoExt . ".avif";

        if(File::exists($afterPath) && File::exists($avifPath) &&
            DB::table('asset_optimize')
              ->where('after_hash', '=', $currentFileHash)
              ->first()){
            if($this->debug){
                Log::info("Skip optimize for unchanged image " . $path);
            }
            return;
        }

        $jpegQuality    = '--max=100';
        $pngQuality     = '--quality=100';
        $optimizerChain = (new OptimizerChain())
            ->addOptimizer(new Jpegoptim([
                $jpegQuality,
                '--strip-all',
                '--all-progressive',
            ]))
            ->addOptimizer(new Pngquant([
                $pngQuality,
                '--force',
                '--skip-if-larger',
            ]))
            ->addOptimizer(new Optipng([
                '-i0',
                '-o2',
                '-quiet',
            ]))
            ->addOptimizer(new Svgo([
                '--disable={cleanupIDs,removeViewBox}',
            ]))
            ->addOptimizer(new Gifsicle([
                '-b',
                '-O3',
            ]))
            ->addOptimizer(new Cwebp([
                '-m 6',
                '-pass 10',
                '-mt',
                '-q 100',
            ]));
        if($this->debug){
            $optimizerChain->useLogger(Log::getLogger());
            Log::info("Optimize image " . $path);
        }

        try {
            File::copy($path, $backupPath);
            $optimizerChain->optimize($path, $afterPath);
            
            $this->convertToAvif($path, $avifPath);

            $relativeAfterPath = str_replace(base_path(), "", $afterPath);
    
            DB::table('asset_optimize')->insert([
                'orig_name'   => $relativePath,
                'orig_hash'   => $currentFileHash,
                'after_name'  => $relativeAfterPath,
                'after_hash'  => hash_file("sha256", $newFileWoExt . "." . $pathParts["extension"]),
                'backup_name' => $relativeBackupPath,
                'old_size'    => filesize($backupPath),
                'new_size'    => filesize($newFileWoExt . "." . $pathParts["extension"]),
            ]);
        }
        catch(InvalidImageTypeException $e)  {

        }
    }

    public function convertToAvif($path, $avifPath)  {

        try {

            $image_info     = getimagesize($path);
            $image_type = $image_info[2];
        
            if($image_type == IMAGETYPE_JPEG){
                $image = imagecreatefromjpeg($path);
            }
            elseif($image_type == IMAGETYPE_GIF){
                $image = imagecreatefromgif($path);
            }
            elseif($image_type == IMAGETYPE_PNG){
                $image = imagecreatefrompng($path);
            }
        
            if(imageavif($image, $avifPath, 95))  {
                return $avifPath;
            }
            else  {
    
                @File::delete($avifPath);
                return false;
            }
        }
        catch(Exception $e)  {
            @File::delete($avifPath);
            return false;
        }
    }

    /**
     * @param string $host
     * @param string $path
     */
    public function enableHtaccessWebpOverwrite(string $host = "", string $path = ""): void{
        if((!$host || !$path) && strpos(PHP_SAPI, 'cli') !== false){
            return;
        }
        if(!$host){
            $host = $_SERVER["HTTP_HOST"];
        }
        if(!$path){
            $path = str_replace(request()->getPathInfo(), "", $_SERVER["REDIRECT_URL"]);
        }

        if($path == "")  {
            $path = "/";
        }

        $htaccessPath    = base_path(".htaccess");
        $htaccessContent = file_get_contents($htaccessPath);
        $htaccessContent = preg_replace("/# --dynamic_line_dont_remove_1--([\s\S]*?)# --dynamic_line_dont_remove_2--/i",
            "# --dynamic_line_dont_remove_1--" . PHP_EOL .
            'SetEnvIf HOST "' . $host . '" BASE_PATH=' . $path . PHP_EOL .
            "# --dynamic_line_dont_remove_2--", $htaccessContent);
        file_put_contents($htaccessPath, $htaccessContent);
    }
}
