<?php return array (
  'intervention/httpauth' => 
  array (
    'aliases' => 
    array (
      'HttpAuth' => 'Intervention\\HttpAuth\\Laravel\\Facades\\HttpAuth',
    ),
    'providers' => 
    array (
      0 => 'Intervention\\HttpAuth\\Laravel\\HttpAuthServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravel/ui' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Ui\\UiServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'riverskies/laravel-mobile-detect' => 
  array (
    'aliases' => 
    array (
      'MobileDetect' => 'Riverskies\\Laravel\\MobileDetect\\Facades\\MobileDetect',
    ),
    'providers' => 
    array (
      0 => 'Riverskies\\Laravel\\MobileDetect\\MobileDetectServiceProvider',
    ),
  ),
  'spatie/laravel-csp' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Csp\\CspServiceProvider',
    ),
  ),
  'spatie/laravel-ignition' => 
  array (
    'aliases' => 
    array (
      'Flare' => 'Spatie\\LaravelIgnition\\Facades\\Flare',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    ),
  ),
  'stolz/assets' => 
  array (
    'providers' => 
    array (
      0 => 'Stolz\\Assets\\Laravel\\ServiceProvider',
    ),
  ),
);