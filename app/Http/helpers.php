<?php
use App\Framework\src\Code\RequestLanguage;
use App\Framework\src\Http\Models\Data;
use App\Framework\src\Http\Models\DataRevision;
use App\Http\Models\Competition;
use App\Http\Models\FindTheMistake;
use Illuminate\Support\Facades\Mail;
use Symfony\Component\HttpFoundation\IpUtils;
use Illuminate\Http\Request;
use App\Mail\SendEmail;
use App\Http\Models\Transactions;
use App\Http\Models\Memory;
use App\Http\Models\GuessTheGame;
use App\Http\Models\MathRiddle;
use App\Http\Models\RewardCode;
use App\Http\Models\Trivia;
use Carbon\Carbon;

function module_text_prefixes()  {

    $prefixArr = [
        'module1' => 'a_module1',    //e.g. a_module1,z_module9
        'module2' => 'b_module2',
    ];

    return $prefixArr;
}

function module_types($module=false, $field=false)  {

    $module_types = [
        'module1' => [
            'title' => 'Module 1',
        ],
        'module2' => [
            'title' => 'Module 2',
        ]
    ];

    if($module !== false)  {

        $module_data = isset($module_types[$module]) ? $module_types[$module] : false;

        if($field !== false)  {
            return isset($module_data[$field]) ? $module_data[$field] : "";
        }

        return $module_data;
    }

    return $module_types;
}

function siteView($view, $is_mobile) {

    if($is_mobile)  {
        return "site.mobile." . $view;
	}

    return "site.desktop." . $view;
}

function setUserLanguage($locale = ""){
    app(RequestLanguage::class)->setLanguageCode($locale);
    session()->put("user_language", getRequestLocale());
}

function getLanguageCodeGroups(){
    return [];
}

function getLanguageGroupFor($locale = ""){ // de_CH
    $locale         = @trim($locale);
    $locale         = $locale ?: getRequestLocale();
    $languageGroups = getLanguageCodeGroups();
    foreach($languageGroups as $languageGroup) if(in_array($locale, $languageGroup)) return $languageGroup;
    return [];
}

function hasLanguageGroups($locale = ""){ // de_CH
    $locale = $locale ?: getRequestLocale();
    return (bool)getLanguageGroupFor($locale);
}

function getRequestLanguageCode(){
    return app(RequestLanguage::class)->getLanguageCode();
}

function getRequestLocale(){
    return app(RequestLanguage::class)->getLanguageLocale();
}

function getRequestRegionCode(){
    return app(RequestLanguage::class)->getLanguageRegion();
}

function getLanguageId($langCode){
    return app(RequestLanguage::class)->getLanguageId($langCode);
}

function getLanguageLocaleIds()  {
    $allowedLanguageCodes = app(RequestLanguage::class)->languageMap;
    return $allowedLanguageCodes;
}

function getAllowedLanguageCodes(){
    return ["de_DE"];
}

function getDefaultLanguageCode(){
    return "de_DE";
}

function devModeActive(){
    $serverName = @$_SERVER["SERVER_NAME"] ?? "";
    return config("app.debug") && in_array($serverName, ["localhost"]);
}

function is_localhost(){
    
    if(devModeActive())
        return @$_SERVER['SERVER_NAME'] == "localhost";
    else
        return false;
}

function test_mode(){

    $serverName = $_SERVER["SERVER_NAME"] ?? "";

    if($serverName == "ps.playstation.com")  {

        $debugOverride = fS("app.allow_debug_login", 1) && isDebugEnabled(request()->ip());
        if($debugOverride){
            return true;
        }
    }

    // @TODO: disable!
    return dev_test_mode();
}

function dev_test_mode(){
    return @$_SERVER['SERVER_NAME'] == "localhost";
}

function readCsvFile($filePath = "", $columnAmount = 2){
    $retData = [];
    if(($handle = fopen($filePath, 'r')) !== false){
        $header = fgetcsv($handle); // First row -> headers
        while(($data = fgetcsv($handle)) !== false){
            $retData[] = $data;
            unset($data);
        }
        fclose($handle);
    }
    return $retData;
}

function get_youtube_video($youtube_id){
    $general_video_info = file_get_contents("https://www.googleapis.com/youtube/v3/videos?id=" . $youtube_id . "&part=snippet,statistics,contentDetails&key=AIzaSyCOEcfRv8C4Xn-JtD5WiymC9AOuPaAxajE");
    $general_json       = json_decode($general_video_info, TRUE);

    $return                 = array();
    $return['name']         = $general_json['items'][0]['snippet']['title'];
    $return['thumbnailUrl'] = $general_json['items'][0]['snippet']['thumbnails']['medium']['url'];

    return $return;
}

function sendPushNotifications($payload, $registration_ids, $fcmAccessToken){

    //chunk registration ids to 1000 and send
    $registration_id_sets = array_chunk($registration_ids, 1000);

    foreach($registration_id_sets as $registration_id_set){

        $url = 'https://fcm.googleapis.com/v1/projects/playstation-gsa-b590f/messages:send';

        $payload['token'] = $registration_id_set[0];
        $fields = array('message' => $payload);

        $fields = json_encode($fields);

        $headers = array(
            'Authorization: Bearer ' . $fcmAccessToken,
            'Content-Type: application/json'
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);

        $result   = curl_exec($ch);
        if (curl_errno($ch)) {
            //echo $error_msg = curl_error($ch);
        }

        $respJson = json_decode($result);
        curl_close($ch);
    }
}

function sendPushNotificationTopic($payload, $topic, $fcmAccessToken){

    $url = 'https://fcm.googleapis.com/v1/projects/playstation-gsa-b590f/messages:send';

    $payload['topic'] = $topic;
    $fields = array('message' => $payload);

    $fields = json_encode($fields);

    $headers = array(
        'Authorization: Bearer ' . $fcmAccessToken,
        'Content-Type: application/json'
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);

    $result   = curl_exec($ch);
    if (curl_errno($ch)) {
        //echo $error_msg = curl_error($ch);
    }

    $respJson = json_decode($result);

    curl_close($ch);
}

function tokenToTopic($is_enabled, $topic, $token)  {

    //add registration token to topic
    if($is_enabled)
        $url = "https://iid.googleapis.com/iid/v1:batchAdd";
    else
        $url = "https://iid.googleapis.com/iid/v1:batchRemove";
    
    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer '.getFcmAccessToken(),
        'access_token_auth: true',
    ];
    
    $apiData = json_encode([
        "to" => "/topics/".$topic,
        "registration_tokens" => [$token]
    ]);

    $topicResp = call_curl($url, "POST", $apiData, $headers);

    $hasError = false;
    if(isset($topicResp['results'][0]['error']))  {
        $hasError = true;
    }
}

function getFcmAccessToken()  {

    $client = new Google_Client();
    try {
        $client->setAuthConfig(base_path("firebase-auth-adminsdk.json"));
        $client->addScope(Google_Service_FirebaseCloudMessaging::CLOUD_PLATFORM);

        // retrieve the saved oauth token if it exists, you can save it on your database or in a secure place on your server
        $savedTokenJson = readFCMAccessTokenFile();
        
        if ($savedTokenJson != null) {
            // the token exists, set it to the client and check if it's still valid
            $client->setAccessToken($savedTokenJson);
            if ($client->isAccessTokenExpired()) {
                // the token is expired, generate a new token and set it to the client
                $accessToken = generateFCMAccessToken($client);
                $client->setAccessToken($accessToken);
            }
            else  {
                $accessToken = $client->getAccessToken();
            }
        } else {
            // the token doesn't exist, generate a new token and set it to the client
            $accessToken = generateFCMAccessToken($client);
            $client->setAccessToken($accessToken);
        }

        // the client is configured, now you can send the push notification using the $oauthToken.
        $oauthToken = $accessToken["access_token"];
        return $oauthToken;

    } catch (Google_Exception $e) {
        // handle exception
    }
    
    return "";
}

function generateFCMAccessToken($client) {

    $client->fetchAccessTokenWithAssertion();
    $accessToken = $client->getAccessToken();
    
    // save the oauth token json on your database or in a secure place on your server
    $tokenJson = json_encode($accessToken);
    saveFCMAccessTokenFile($tokenJson);

    return $accessToken;
}

function readFCMAccessTokenFile()  {

    $accessTokenJson = null;
    $filePath = base_path("firebase-access-token.json");

    $myfile = @fopen($filePath, "r");
    if($myfile)  {

        $accessTokenJson = json_decode(fread($myfile, filesize($filePath)), true);
        fclose($myfile);
    }
    
    return $accessTokenJson;
}

function saveFCMAccessTokenFile($tokenJson)  {
    
    $filePath = base_path("firebase-access-token.json");
    $myfile = @fopen($filePath, "w");

    if($myfile)  {

        fwrite($myfile, $tokenJson);
        fclose($myfile);
    }
}

function get_youtube_id($url) {
	// Here is a sample of the URLs this regex matches: (there can be more content after the given URL that will be ignored)

	// http://youtu.be/dQw4w9WgXcQ
	// http://www.youtube.com/embed/dQw4w9WgXcQ
	// http://www.youtube.com/watch?v=dQw4w9WgXcQ
	// http://www.youtube.com/?v=dQw4w9WgXcQ
	// http://www.youtube.com/v/dQw4w9WgXcQ
	// http://www.youtube.com/e/dQw4w9WgXcQ
	// http://www.youtube.com/user/username#p/u/11/dQw4w9WgXcQ
	// http://www.youtube.com/sandalsResorts#p/c/54B8C800269D7C1B/0/dQw4w9WgXcQ
	// http://www.youtube.com/watch?feature=player_embedded&v=dQw4w9WgXcQ
	// http://www.youtube.com/?feature=player_embedded&v=dQw4w9WgXcQ

	// It also works on the youtube-nocookie.com URL with the same above options.
	// It will also pull the ID from the URL in an embed code (both iframe and object tags)

	preg_match('%(?:youtube(?:-nocookie)?\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})%i', $url, $match);
	$youtube_id = @$match[1];

	return $youtube_id;
}

function reverseSanitizeString($text){

    return html_entity_decode($text,ENT_QUOTES);
}

function sendSMTPEmail($mail_name, $mail_data)  {

    // Fetch dynamic SMTP details (e.g., from database or user input)
    config()->set('mail.default', fS('app.mail.mailer', 'smtp'));

    config()->set('mail.mailers.smtp.host', fS('app.mail.host', 'mail.contentcreators.sk'));
    config()->set('mail.mailers.smtp.port', fS('app.mail.port', 587));
    config()->set('mail.mailers.smtp.encryption', fS('app.mail.encryption', 'TLS'));
    config()->set('mail.mailers.smtp.username', fS('app.mail.username', '<EMAIL>'));
    config()->set('mail.mailers.smtp.password', fS('app.mail.password', 'ber37FQCc6ak'));

    Mail::send(new SendEmail($mail_name, $mail_data));
}

function convertExcelDate($dateValue, $format='Y-m-d H:i:s') {

    $unixDate = ceil(($dateValue - 25569) * 86400);
    return gmdate($format, $unixDate);
}

function pagination_links($pagination)  {
    
    $currentPage = $pagination->currentPage();
    $totalPages = $pagination->lastPage();
    $link = $pagination->path();

    $adjacents = 2;

    $pagination_links = '<ul class="cm-pagination">';
    $max_links_to_show = (($adjacents*2) + 1);

    if ($totalPages <= $max_links_to_show) {

        for ($counter = 1; $counter <= $totalPages; $counter++) {

            if ($counter == $currentPage) {
                $pagination_links.= "<li class='active'><a role='button'>".$counter."</a></li>"; 
            }
            else    {
                $pagination_links.= "<li><a role='button' href='".$link."?page=".$counter."'>".$counter."</a></li>";
            }
        }
    }
    elseif ($totalPages > $max_links_to_show)  {

        if($currentPage <= 4) {

            for ($counter = 1; $counter <= ($currentPage + $adjacents); $counter++)  {

                if ($counter == $currentPage) {
                    $pagination_links.= "<li class='active'><a role='button' href='".$link."?page=".$counter."'>".$counter."</a></li>";
                }
                else {
                    $pagination_links.= "<li><a role='button' href='".$link."?page=".$counter."'>".$counter."</a></li>";
                }
            }

            $pagination_links.= "<li>...</li>";
            $pagination_links.= "<li><a role='button' href='".$link."?page=".$totalPages."'>".$totalPages."</a></li>";
        }
        elseif($currentPage > 2 && $currentPage < $totalPages - ($adjacents + 1)) { 

            $pagination_links.= "<li><a role='button' href='?=1'>1</a></li>";
            $pagination_links.= "<li>...</li>";

            for($counter = $currentPage - $adjacents; $counter <= $currentPage + $adjacents; $counter++) { 
                if ($counter == $currentPage) {
                    $pagination_links.= "<li class='active'><a role='button'>".$counter."</a></li>"; 
                }
                else{
                    $pagination_links.= "<li><a role='button' href='".$link."?page=".$counter."'>".$counter."</a></li>";
                }                  
            }

            $pagination_links.= "<li>...</li>";
            $pagination_links.= "<li><a role='button' href='".$link."?page=".$totalPages."'>".$totalPages."</a></li>";
        }
        else {

            $pagination_links.= "<li><a role='button' href='".$link."?page=1'>1</a></li>";
            $pagination_links.= "<li>...</li>";

            for ($counter = $totalPages - 5; $counter <= $totalPages; $counter++) {

                if ($counter == $currentPage) {
                    $pagination_links.= "<li class='active'><a role='button'>".$counter."</a></li>"; 
                }
                else{
                    $pagination_links.= "<li><a role='button' href='".$link."?page=".$counter."'>".$counter."</a></li>";
                }                   
            }
        }
    }

    $pagination_links.= "</ul>";

    return $pagination_links;
}

function deFormatToStandard($date)  {

    $dateParts = explode(".", $date);
    return $dateParts[2]."-".$dateParts[1]."-".$dateParts[0];
}

function getAdminDefaultImage($username){

    $first_letter = strtoupper($username[0]);
    $bg_classes = ['bg-warning', 'bg-primary', 'bg-success', 'bg-danger', 'bg-info'];
    $index = ord($first_letter) % count($bg_classes);
    $bg_class = $bg_classes[$index];

    return '<div class="avatar-image me-2 ' . $bg_class . ' text-white">' . $first_letter . '</div>';
}

function lang_month($date) {

    $months = array(
        "January"   => "Januar",
        "February"  => "Februar",
        "March"     => "März",
        "April"     => "April",
        "May"       => "Mai",
        "June"      => "Juni",
        "July"      => "Juli",
        "August"    => "August",
        "September" => "September",
        "October"   => "Oktober",
        "November"  => "November",
        "December"  => "Dezember",
        "Jan"   => "Jan",
        "Feb"   => "Feb",
        "Mar"   => "Mär",
        "Apr"   => "Apr",
        "May"   => "Mai",
        "Jun"   => "Jun",
        "Jul"   => "Jul",
        "Aug"   => "Aug",
        "Sep"   => "Sep",
        "Oct"   => "Okt",
        "Nov"   => "Nov",
        "Dec"   => "Dez",
    );

    foreach($months as $en_name => $de_name){
        $date = str_replace($en_name, $de_name, $date);
    }

    return $date;
}

function langField($val, $langCode=false)  {

    if(!$langCode)
        $langCode = getRequestLanguageCode();

    $langValues = $val ? @json_decode($val, true) : false;

    return $langValues ? @$langValues[$langCode] : $val;
}

function langColumnValues($val)  {

    $langArray = $val ? @json_decode($val, true) : [];

    if($langArray)  {

        $str = '';

        foreach($langArray as $key => $name)  {
            $str .= strtoupper($key). ' : ' .$name.'<br>';
        }

        return $str;
    }
    else  {
        return $val;
    }
}

function cc_json_encode($arr, $type='text')  {

    foreach($arr as $key => $val)  {

        if($type == 'editor')
            $arr[$key] = sanitizeHTMLStr($val);
        else
            $arr[$key] = sanitizeStr($val);

    }

    return json_encode($arr);
}

function langLabel($str, $langCode)  {

    $langNames = [
        "de" => "German",
        "en" => "English",
        "pl" => "Polish",
    ];

    return $str . " (".$langNames[$langCode].")";
}

function getCacheRevision(){
    return DataRevision::getRevision('cache.revision');
}

function getPictureFileName($originalFilename, $slug){

    if($originalFilename)  {
        $fileInfo = pathinfo($originalFilename);
        $newFilename = $fileInfo['filename'] . $slug . $fileInfo['extension'];
        return  $newFilename;
    }
    else  {
        return "";
    }
}

//give transaction json for on change title and description show
function getTransactionsJson()  {

	$transactions_arr = Transactions::all()->keyBy('id')->ToArray();

	foreach($transactions_arr as $tKey => $transaction)  {
		$transactions_arr[$tKey]['title'] = $transaction['title'];
		$transactions_arr[$tKey]['body_text'] = $transaction['body_text'];
	}

	return json_encode($transactions_arr);
}

function competitionStartDate()  {

    return fS('competition.start_date','2025-04-29');
}

function competitionEndDate()  {

    return fS('competition.end_date', '2025-05-12');
}

function competitionDayDate($dayNum)  {

    $start_date = competitionStartDate();

    $data_day = Carbon::createFromFormat("Y-m-d", $start_date)->addDays(($dayNum-1))->format("Y-m-d");

    return $data_day;
}

function competitionDayCounts(){
    $start = fS('competition.start_date','2025-04-29');
    $end   = fS('competition.end_date', '2025-05-12');

    $startDate = new DateTime($start);
    $endDate = new DateTime($end);

    // Add one day to include both start and end dates
    $interval = $startDate->diff($endDate)->days + 1;

    return $interval;

}

function isDebugEnabled($ip_address)  {

    $serverName = $_SERVER["SERVER_NAME"] ?? "";

    if($serverName == "ps.playstation.com") {

        $allowedIpsJson = Data::getData("maintenance_mode_ips");
        $allowedIpsJson = $allowedIpsJson ? json_decode($allowedIpsJson, true) : [];
        $settingsAllowedIps = array_map("trim", explode(",", fS("app_settings.allowed_ips", "*************, *************")));

        $allowedIpsJson = array_merge($allowedIpsJson, $settingsAllowedIps);

        if($allowedIpsJson && IpUtils::checkIp($ip_address, (array)$allowedIpsJson))
            return true;
    }
    else if(test_mode() && $serverName != "ps.playstation.com")  {
        return true;
    }

    return false;
}

function get_calendar_date()  {

    $today = date('Y-m-d', time());
    $is_test_mode = test_mode();

    if($is_test_mode && isset($_GET['date'])){
        $today = $_GET['date'];
    }
    else if($is_test_mode && isset($_POST['test_calendar_date'])){
        $today = $_POST['test_calendar_date'];
    }
    else if($is_test_mode && session()->has("debug_mode_sec")){
        $today = session()->get("debug_mode_sec");
    }
    else if($is_test_mode && session()->has("debug_mode")){	//test_mode() &&  TODO - Add this condition on live
        $today = session()->get("debug_mode");
    }

    //TODO - remove when site is live
    if(isset($_GET['date']) && $is_test_mode)  {

        $today = $_GET['date'];
        \App\Framework\src\Http\Models\Log::add("get_calendar_date::override()", var_export($today, true));
        session()->put("debug_mode_sec", $today);
    }

    return $today;
}

function current_time()  {

    $time = strtotime(get_calendar_date() ." ". date('H:i:s', time()));

    if(session()->has("debug_mode_hours")){
        $time = strtotime(get_calendar_date() ." ".session()->get("debug_mode_hours"));
    }

    if(isset($_GET['time']) && test_mode())  {

        $time = strtotime(get_calendar_date() ." ".$_GET['time']);
        session()->put("debug_mode_hours", $_GET['time']);
    }

    return $time;
}

function loginRedirectUrl()  {

    $redirectUrl = session()->has("last_page_url") ? session()->get("last_page_url") : route("site_dashboard");
    session()->forget("last_page_url");

    return $redirectUrl;
}

function app_redirect($request_method, $route_path, $ajax, $ajax_action)  {

    if($request_method == "POST")  {

        $ajax->action($ajax_action);
        return response()->json($ajax->getOutJson());
    }
    else  {

        session()->put("last_page_url", @request()->fullUrl());
        return redirect($route_path);
    }
}

function throttleEnabled()  {

    if(devModeActive())  {
        return false;
    }
    if(PHP_SAPI === 'cli'){
        return false;
    }

    if(fS('enable_throttling', true)){
        return true;
    }
    return false;
}

function userTrophyRank($trophy_level)  {
    
    if($trophy_level < 900)  {
        $trophy_rank = floor($trophy_level / 100) + 1;
    }
    else if($trophy_level < 999)  {
        $trophy_rank = 9;
    }
    else if($trophy_level >= 999)  {
        $trophy_rank = 10;
    }
    
    return $trophy_rank;
}

function animationCountHtmls($count, $class)  {

    $str = '<span class="'.$class.'">0</span>';

    if($count > 0)  {

        $lastStepCount = 0;
        $lastCount = 0;
        $stepCount = $count/6;

        for($si=1; $si<=5; $si++)  {

            $lastStepCount += $stepCount;
            $newCount = round($lastStepCount);

            if($lastCount != $newCount && $newCount != $count)  {

                $lastCount = round($lastStepCount);
                $str .= '<span class="'.$class.'">'.$lastCount.'</span>';
            }
        }

        $str .= '<span class="'.$class.'">'.$count.'</span>';
    }
    
    return $str;
}

function moduleLinkData()  {

    $arr = [
        "memory" => [
            'title' => fT('e_dashboard.d_menu.a_memory','Memory'),
            'icon' => 'memory-icon.jpg',
            'sub_headline' => fT('e_dashboard.d_menu.b_memory_sub_headline','Memory Sub Headline'),
        ],
        "hardware-quiz" => [
            'title' => fT('e_dashboard.d_menu.c_hardware_quiz','Hardware Quiz'),
            'icon' => 'hardware-quiz-icon.jpg',
            'sub_headline' => fT('e_dashboard.d_menu.d_hardware_quiz_sub_headline','Hardware Quiz Sub Headline'),
        ],
        "guess-the-game" => [
            'title' => fT('e_dashboard.d_menu.e_guess_the_game','Guess the game'),
            'icon' => 'guess-the-game-icon.jpg',
            'sub_headline' => fT('e_dashboard.d_menu.f_guess_the_game_sub_headline','Guess the game Sub Headline'),
        ],
        "math-riddle" => [
            'title' => fT('e_dashboard.d_menu.g_math_riddle','Math Riddle'),
            'icon' => 'math-riddle-icon.jpg',
            'sub_headline' => fT('e_dashboard.d_menu.h_math_riddle_sub_headline','Math Riddle Sub Headline'),
        ],
        "find-the-mistakes" => [
            'title' => fT('e_dashboard.d_menu.i_find_the_mistakes','Find The Mistakes'),
            'icon' => 'find-the-mistakes-icon.jpg',
            'sub_headline' => fT('e_dashboard.d_menu.j_find_the_mistakes_sub_headline','Find The Mistakes Sub Headline'),
        ],
        "faq" => [
            'title' => fT('e_dashboard.d_menu.k_faq','FAQ'),
            'icon' => 'faq-icon.jpg',
            'sub_headline' => fT('e_dashboard.d_menu.l_faq_sub_headline','FAQ Sub Headline'),
            //'desktop_cls' => 'white-bg'
        ],
    ];

    return $arr;
}

function active_class($menuPage, $active_page)  {

    $activeCls = '';

    if($menuPage == $active_page)  {
        $activeCls = "active";
    }
    else if($active_page == "memory")  {
        $activeCls = $menuPage == "memory" ? 'active' : '';
    }
    else if($active_page == "hardware-quiz")  {
        $activeCls = $menuPage == "hardware-quiz" ? 'active' : '';
    }
    else if($active_page == "guess-the-game")  {
        $activeCls = $menuPage == "guess-the-game" ? 'active' : '';
    }
    else if($active_page == "math-riddle")  {
        $activeCls = $menuPage == "math-riddle" ? 'active' : '';
    }
    else if($active_page == "find-the-mistakes")  {
        $activeCls = $menuPage == "find-the-mistakes" ? 'active' : '';
    }
    
    return $activeCls;
}

function site_pages_arr($is_mobile=false)  {

    if($is_mobile)  {

        $site_pages_arr = [
            'dashboard'         => ['add_page' => true],
            'memory'            => ['add_page' => false],
            'hardware-quiz'     => ['add_page' => false],
            'guess-the-game'    => ['add_page' => false],
            'math-riddle'       => ['add_page' => false],
            'find-the-mistakes' => ['add_page' => false],
            'faq'               => ['add_page' => false],
            
            'participate'       => ['add_page' => false],
            'participate-thank-you' => ['add_page' => false],
            'notifications'     => ['add_page' => false],
            
            //'transactions'      => ['add_page' => false],
        ];
    }
    else  {

        $site_pages_arr = [
            //'dashboard'         => ['add_page' => true],
            'memory'            => ['add_page' => false],
            'hardware-quiz'     => ['add_page' => false],
            'guess-the-game'    => ['add_page' => false],
            'math-riddle'       => ['add_page' => false],
            'find-the-mistakes' => ['add_page' => false],
            'faq'               => ['add_page' => false],

            'participate'       => ['add_page' => false],
            'participate-thank-you' => ['add_page' => false],
            'notifications'     => ['add_page' => false],
            //'transactions'      => ['add_page' => false],
        ];
    }

    return $site_pages_arr;
}
function get_new_codes($reward_id)  {

    $return = false;
    $api_call_running = fS("digital_reward.api_call_running", "false");

    if($api_call_running == "false")  {

        fS("digital_reward.api_call_running", "true", true);

        try  {

            //get reward code
            $postData = [
                'reward_id' => $reward_id,
            ];

            $api_resp = digital_reward_api("reward/get-codes", "POST", $postData);
            
            if(is_array($api_resp) && isset($api_resp['codes']))  {

                if(count($api_resp['codes']) > 0)  {

                    foreach($api_resp['codes'] as $codeData)  {

                        $reward_code                         = new RewardCode();
                        $reward_code->reward_id              = $reward_id;
                        $reward_code->digital_reward_code_id = $codeData['id'];
                        $reward_code->code                   = $codeData['code'];
                        $reward_code->save();
                    }
                }
                else  {
                    //TODO - send mail to admin for code error
                }

                $return = true;
            }

            //$api_resp == "API Error 3 : Reward codes are over!"
        }
        catch(Exception $e)  {

        }

        //mark call as updated
        fS("digital_reward.api_call_running", "false", true);
    }

    return $return;
}

function revert_codes($ids){

    $reward_code_ids    = array_keys($ids);
    $digital_reward_ids = array_values($ids);

    //revert unused reward codes
    $postData = [
        'ids' => json_encode($digital_reward_ids),
    ];

    $api_resp = digital_reward_api("reward/revert-codes", "POST", $postData);

    if(is_array($api_resp)){

        //delete reward codes from our database
        $chunks = array_chunk($reward_code_ids, 50);
        foreach($chunks as $chunk){
            RewardCode::whereIn('id', $chunk)->delete();
        }

        return true;
    }
    else{
        //TODO - send mail to admin for code error
        return false;
    }
}

function sync_digital_reward_codes($rewardCodes){

    //get reward code
    $postData = [
        'codes' => json_encode($rewardCodes),
    ];

    $api_resp = digital_reward_api("reward/sync-codes", "POST", $postData);

    if(is_array($api_resp)){
        return true;
    }
    else{
        //TODO - send mail to admin for code error
        return false;
    }
}

function digital_reward_api($slug, $method = "GET", $postData = []){

    $api_token = fS("digital_reward.api_token", "11|gLIcJrWYYIqPRHHSf6UZQZAG3AlpK1p1GJKxvgMb");

    $headers = [
        'Authorization: Bearer ' . $api_token,
    ];

    $base_url = fS("digital_reward.api_base_url", "https://ps.playstation.com/digital-rewards-db/api/");
    $api_url  = $base_url . $slug;

    $response = call_curl($api_url, $method, $postData, $headers);
    $response = json_decode($response, true);

    if(isset($response['meta']['status']) && $response['meta']['status'] == "success"){
        return $response['data'];
    }
    else{

        if(isset($response['error']) && $response['error'] != ""){
            return "API Error 401 : " . $response['error'];
        }
        else{
            return isset($response['data']['message']) ? "API Error " . $response['meta']['error_key'] . " : " . $response['data']['message'] : "API Error 0 : Digital reward api failed to send response!";
        }
    }
}

function fillOutData($start_date, $end_date) {
    
    $start = strtotime($start_date);
    $end = strtotime($end_date);

    for ($current = $start; $current <= $end; $current = strtotime("+1 day", $current)) {
        
        $current_date = date('Y-m-d', $current);
        $date_disp = date('d.m', $current);

        $memory = Memory::where('day', $current_date)->first();
        if(!$memory)  {

            $memory = new Memory();
            $memory->code = random_string(20);
            $memory->title  = 'Memory '.$date_disp;
            $memory->day 	= sanitizeStr($current_date);
            $memory->save();
        }

        //remove records NOT between start and end date
        Memory::where(function($query) use ($start_date, $end_date) {
            $query->where('day', '<', $start_date)
                ->orWhere('day', '>', $end_date);
        })->delete();

        
        $guessTheGame = GuessTheGame::where('day', $current_date)->first();
        if(!$guessTheGame)  {

            $guessTheGame = new GuessTheGame();
            $guessTheGame->code = random_string(20);
            $guessTheGame->title  = 'Guess The Game '.$date_disp;
            $guessTheGame->day 	= sanitizeStr($current_date);
            $guessTheGame->save();
        }

        //remove records NOT between start and end date
        GuessTheGame::where(function($query) use ($start_date, $end_date) {
            $query->where('day', '<', $start_date)
                ->orWhere('day', '>', $end_date);
        })->delete();


        /*
        $rec = Trivia::where('day', $current_date)->first();
        if(!$rec)  {

            $rec = new Trivia();
            $rec->day 	= sanitizeStr($current_date);
            $rec->code = random_string(20);
            $rec->quiz_title  = 'Hardware Quiz '.$date_disp;
            $rec->question  = 'Hardware Quiz '.$date_disp.' Question?';
            $rec->answer1  = 'Answer 1';
            $rec->answer2  = 'Answer 2';
            $rec->answer3  = 'Answer 3';
            $rec->answer4  = 'Answer 4';
            $rec->save();
        }

         //remove records NOT between start and end date
         Trivia::where(function($query) use ($start_date, $end_date) {
            $query->where('day', '<', $start_date)
                ->orWhere('day', '>', $end_date);
        })->delete();

        
        
        $rec = FindTheMistake::where('day', $current_date)->first();
        if(!$rec)  {

            $rec = new FindTheMistake();
            $rec->day 	= sanitizeStr($current_date);
            $rec->code = random_string(20);
            $rec->title  = 'Find The Mistake '.$date_disp;
            $rec->error_spots  = '[{"x":71.60970922459893,"y":67.09001782531195},{"x":38.7533422459893,"y":23.12091503267974},{"x":32.53258689839572,"y":28.34967320261438},{"x":34.29144385026738,"y":52.82976827094474},{"x":44.86129679144385,"y":81.11259655377302}]';
            $rec->save();
        }

        //remove records NOT between start and end date
        FindTheMistake::where(function($query) use ($start_date, $end_date) {
            $query->where('day', '<', $start_date)
                ->orWhere('day', '>', $end_date);
        })->delete();

        
        
        $rec = MathRiddle::where('day', $current_date)->first();
        if(!$rec)  {

            $rec = new MathRiddle();
            $rec->day 	= sanitizeStr($current_date);
            $rec->code = random_string(20);
            $rec->math_riddle_title  = 'Math Riddle '.$date_disp;
            $rec->answer  = 50;
            $rec->save();
        }

        //remove records NOT between start and end date
        MathRiddle::where(function($query) use ($start_date, $end_date) {
            $query->where('day', '<', $start_date)
                ->orWhere('day', '>', $end_date);
        })->delete();

        
        
        $rec = Competition::where('date', $current_date)->first();
        if(!$rec)  {

            $rec = new Competition();
            $rec->date 	= sanitizeStr($current_date);
            $rec->code = random_string(20);
            $rec->prize_name  = 'Prize '.$date_disp;
            $rec->prize_description  = 'Prize description text for day '.$date_disp.'.';
            $rec->avtar_name  = 'Avatar '.$date_disp;
            $rec->code_type  = "fixed";
            $rec->avtar_code  = "XXXX-XXXX-".str_replace(".", "", $date_disp);
            $rec->save();
        }

        //remove records NOT between start and end date
        Competition::where(function($query) use ($start_date, $end_date) {
            $query->where('date', '<', $start_date)
                ->orWhere('date', '>', $end_date);
        })->delete();
        */
    }
}

function ccImgUrl($image, $default_img, $absolute_path=false)  {

    if($absolute_path) {
        $image_path = $image ? public_path('uploads/'.$image) : public_path('gfx/'.$default_img);
    }
    else  {

        $filePath = public_path('uploads') . DIRECTORY_SEPARATOR . $image;
        if($image && File::exists($filePath))
            $image_path = url('public/uploads/'.$image); 
        else
            $image_path = url('public/gfx/'.$default_img);
    }
    return $image_path;
}

function advisoryLockElse($lockName, $timeoutSec, callable $callback, callable $failureCallback){
    $timeoutSec = is_numeric($timeoutSec) ? (int)$timeoutSec : 10;
    $lockName   = $lockName ?: @(string)@request()->ip();
    $lockName   = $lockName ?: @(string)time();
    $lockName   = substr($lockName, -64);
    // Lock for at most 10 seconds.  This is the MySQL >5.7.5 implementation.
    // Older MySQL versions have some weird behavior with GET_LOCK().
    // Other databases have a different implementation.
    try{
        \DB::statement("SELECT GET_LOCK('" . $lockName . "', " . (int)$timeoutSec . ")");
    }
    catch(Exception $e){
        Log::error($e);
        return $failureCallback();
    }
    $output = $callback();
    try{
        \DB::statement("SELECT RELEASE_LOCK('" . $lockName . "')");
    }
    catch(Exception $e){
        Log::error($e);
    }
    return $output;
}

function avgCount($counts, $unique){
    return $unique > 0 ? $counts / $unique : 0;
}

function hyphenate($str, $char_counts=3) {

    $str = str_replace("-", "", $str);
    return implode("-", str_split($str, $char_counts));
}