<?php if($page_type != "dashboard" || $is_mobile): ?>
    <?php echo $__env->make('site.elements.ps-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php endif; ?>


<?php if($page_type == "dashboard" && !$is_mobile): ?>

    <?php  $site_pages_arr = site_pages_arr();  ?>

    <?php $__currentLoopData = $site_pages_arr; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sitePageSlug => $sitePageArr): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

        <?php  $make_page_active = $active_page == $sitePageSlug ? true : false;  ?>

        <div class="offcanvas offcanvas-end cc-<?php echo e($sitePageSlug); ?> <?php echo e(in_array($sitePageSlug, ['faq', 'notifications', 'participate', 'participate-thank-you']) ? ' white-bg' : ''); ?><?php echo e($make_page_active ? ' show' : ''); ?>" data-bs-scroll="true" id="cc-<?php echo e($sitePageSlug); ?>" aria-labelledby="cc-<?php echo e($sitePageSlug); ?>Label">

            <?php if($sitePageArr['add_page'] || $make_page_active): ?>
                <?php echo $__env->make("site.desktop.pages." . $sitePageSlug, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

        </div>
        
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    
<?php endif; ?>


<?php if($page_type == "dashboard" && $is_mobile): ?>

    <?php echo $__env->make('site.mobile.elements.splash-screen', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->make('site.mobile.elements.install-popup', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php endif; ?>

<input type="hidden" id="is_mobile" value="<?php echo e($is_mobile ? 'Y' : 'N'); ?>"><?php /**PATH S:\contentcreators\svn\2018\clients\sied\dop-2025\resources\views/site/elements/footer.blade.php ENDPATH**/ ?>