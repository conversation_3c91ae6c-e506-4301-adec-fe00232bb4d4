[2025-05-07 19:35:18] production.ERROR: Exception: The S:\contentcreators\svn\2018\clients\sied\dop-2025\bootstrap\cache directory must be present and writable. in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php:178
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(131): Illuminate\Foundation\PackageManifest->write(Array)
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(107): Illuminate\Foundation\PackageManifest->build()
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(90): Illuminate\Foundation\PackageManifest->getManifest()
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(79): Illuminate\Foundation\PackageManifest->config('aliases')
#4 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(286): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(447): Illuminate\Foundation\Application->bootstrapWith(Array)
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(199): Illuminate\Foundation\Console\Kernel->bootstrap()
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\artisan(34): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#9 {main}  
[2025-05-07 19:35:18] production.ERROR: The S:\contentcreators\svn\2018\clients\sied\dop-2025\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\bootstrap\\cache directory must be present and writable. at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:178)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-05-07 19:35:19] production.ERROR: Exception: The S:\contentcreators\svn\2018\clients\sied\dop-2025\bootstrap\cache directory must be present and writable. in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php:178
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(131): Illuminate\Foundation\PackageManifest->write(Array)
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(107): Illuminate\Foundation\PackageManifest->build()
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(90): Illuminate\Foundation\PackageManifest->getManifest()
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(79): Illuminate\Foundation\PackageManifest->config('aliases')
#4 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(286): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(447): Illuminate\Foundation\Application->bootstrapWith(Array)
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(199): Illuminate\Foundation\Console\Kernel->bootstrap()
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\artisan(34): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#9 {main}  
[2025-05-07 19:35:19] production.ERROR: The S:\contentcreators\svn\2018\clients\sied\dop-2025\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\bootstrap\\cache directory must be present and writable. at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:178)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-05-07 19:41:24] production.ERROR: Exception: The S:\contentcreators\svn\2018\clients\sied\dop-2025\bootstrap\cache directory must be present and writable. in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php:178
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(131): Illuminate\Foundation\PackageManifest->write(Array)
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(107): Illuminate\Foundation\PackageManifest->build()
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(90): Illuminate\Foundation\PackageManifest->getManifest()
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(79): Illuminate\Foundation\PackageManifest->config('aliases')
#4 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(286): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(447): Illuminate\Foundation\Application->bootstrapWith(Array)
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(199): Illuminate\Foundation\Console\Kernel->bootstrap()
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\artisan(34): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#9 {main}  
[2025-05-07 19:41:24] production.ERROR: The S:\contentcreators\svn\2018\clients\sied\dop-2025\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\bootstrap\\cache directory must be present and writable. at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:178)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-05-07 19:41:29] production.ERROR: PDOException: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php:65
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', Object(SensitiveParameterValue), Array)
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php(44): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\ConnectionFactory.php(184): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(1339): call_user_func(Object(Closure))
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(1375): Illuminate\Database\Connection->getPdo()
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(528): Illuminate\Database\Connection->getReadPdo()
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(423): Illuminate\Database\Connection->getPdoForSelect(true)
#9 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(816): Illuminate\Database\Connection->Illuminate\Database\{closure}('select `value` ...', Array)
#10 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#11 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select `value` ...', Array, Object(Closure))
#12 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select `value` ...', Array, true)
#13 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#14 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#15 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#16 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#17 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\DataRevision.php(36): Illuminate\Database\Query\Builder->first()
#18 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(71): App\Framework\src\Http\Models\DataRevision::getRevision()
#19 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(62): App\Framework\src\Http\Models\Setting::initSettingMap()
#20 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\functions.php(69): App\Framework\src\Http\Models\Setting::getSetting('extension.owner...', '66898008')
#21 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Console\Commands\DispatchChatInit.php(49): fS('extension.owner...', '66898008')
#22 [internal function]: App\Console\Commands\DispatchChatInit->__construct()
#23 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(952): ReflectionClass->newInstanceArgs(Array)
#24 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(795): Illuminate\Container\Container->build('App\\Console\\Com...')
#25 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(986): Illuminate\Container\Container->resolve('App\\Console\\Com...', Array, true)
#26 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(731): Illuminate\Foundation\Application->resolve('App\\Console\\Com...', Array)
#27 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(971): Illuminate\Container\Container->make('App\\Console\\Com...', Array)
#28 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(251): Illuminate\Foundation\Application->make('App\\Console\\Com...')
#29 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(349): Illuminate\Console\Application->resolve('App\\Console\\Com...')
#30 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(130): Illuminate\Foundation\Console\Kernel->Illuminate\Foundation\Console\{closure}(Object(Illuminate\Console\Application))
#31 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(77): Illuminate\Console\Application->bootstrap()
#32 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(481): Illuminate\Console\Application->__construct(Object(Illuminate\Foundation\Application), Object(Illuminate\Events\Dispatcher), '10.48.29')
#33 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Illuminate\Foundation\Console\Kernel->getArtisan()
#34 S:\contentcreators\svn\2018\clients\sied\dop-2025\artisan(34): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `value` from `data_revision` where `key` = main.revision limit 1) in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php:829
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select `value` ...', Array, Object(Closure))
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select `value` ...', Array, true)
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\DataRevision.php(36): Illuminate\Database\Query\Builder->first()
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(71): App\Framework\src\Http\Models\DataRevision::getRevision()
#9 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(62): App\Framework\src\Http\Models\Setting::initSettingMap()
#10 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\functions.php(69): App\Framework\src\Http\Models\Setting::getSetting('extension.owner...', '66898008')
#11 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Console\Commands\DispatchChatInit.php(49): fS('extension.owner...', '66898008')
#12 [internal function]: App\Console\Commands\DispatchChatInit->__construct()
#13 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(952): ReflectionClass->newInstanceArgs(Array)
#14 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(795): Illuminate\Container\Container->build('App\\Console\\Com...')
#15 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(986): Illuminate\Container\Container->resolve('App\\Console\\Com...', Array, true)
#16 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(731): Illuminate\Foundation\Application->resolve('App\\Console\\Com...', Array)
#17 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(971): Illuminate\Container\Container->make('App\\Console\\Com...', Array)
#18 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(251): Illuminate\Foundation\Application->make('App\\Console\\Com...')
#19 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(349): Illuminate\Console\Application->resolve('App\\Console\\Com...')
#20 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(130): Illuminate\Foundation\Console\Kernel->Illuminate\Foundation\Console\{closure}(Object(Illuminate\Console\Application))
#21 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(77): Illuminate\Console\Application->bootstrap()
#22 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(481): Illuminate\Console\Application->__construct(Object(Illuminate\Foundation\Application), Object(Illuminate\Events\Dispatcher), '10.48.29')
#23 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Illuminate\Foundation\Console\Kernel->getArtisan()
#24 S:\contentcreators\svn\2018\clients\sied\dop-2025\artisan(34): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 {main}  
[2025-05-07 19:41:29] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `value` from `data_revision` where `key` = main.revision limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `value` from `data_revision` where `key` = main.revision limit 1) at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `value` ...', Array, Object(Closure))
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `value` ...', Array, true)
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\DataRevision.php(36): Illuminate\\Database\\Query\\Builder->first()
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(71): App\\Framework\\src\\Http\\Models\\DataRevision::getRevision()
#9 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(62): App\\Framework\\src\\Http\\Models\\Setting::initSettingMap()
#10 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\functions.php(69): App\\Framework\\src\\Http\\Models\\Setting::getSetting('extension.owner...', '66898008')
#11 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Console\\Commands\\DispatchChatInit.php(49): fS('extension.owner...', '66898008')
#12 [internal function]: App\\Console\\Commands\\DispatchChatInit->__construct()
#13 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): ReflectionClass->newInstanceArgs(Array)
#14 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Console\\\\Com...')
#15 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Console\\\\Com...', Array, true)
#16 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Console\\\\Com...', Array)
#17 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Console\\\\Com...', Array)
#18 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(251): Illuminate\\Foundation\\Application->make('App\\\\Console\\\\Com...')
#19 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(349): Illuminate\\Console\\Application->resolve('App\\\\Console\\\\Com...')
#20 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(130): Illuminate\\Foundation\\Console\\Kernel->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Console\\Application))
#21 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(77): Illuminate\\Console\\Application->bootstrap()
#22 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(481): Illuminate\\Console\\Application->__construct(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Events\\Dispatcher), '10.48.29')
#23 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Illuminate\\Foundation\\Console\\Kernel->getArtisan()
#24 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', Object(SensitiveParameterValue), Array)
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `value` ...', Array)
#10 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#11 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `value` ...', Array, Object(Closure))
#12 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `value` ...', Array, true)
#13 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#17 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\DataRevision.php(36): Illuminate\\Database\\Query\\Builder->first()
#18 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(71): App\\Framework\\src\\Http\\Models\\DataRevision::getRevision()
#19 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(62): App\\Framework\\src\\Http\\Models\\Setting::initSettingMap()
#20 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\functions.php(69): App\\Framework\\src\\Http\\Models\\Setting::getSetting('extension.owner...', '66898008')
#21 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Console\\Commands\\DispatchChatInit.php(49): fS('extension.owner...', '66898008')
#22 [internal function]: App\\Console\\Commands\\DispatchChatInit->__construct()
#23 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): ReflectionClass->newInstanceArgs(Array)
#24 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Console\\\\Com...')
#25 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Console\\\\Com...', Array, true)
#26 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Console\\\\Com...', Array)
#27 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Console\\\\Com...', Array)
#28 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(251): Illuminate\\Foundation\\Application->make('App\\\\Console\\\\Com...')
#29 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(349): Illuminate\\Console\\Application->resolve('App\\\\Console\\\\Com...')
#30 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(130): Illuminate\\Foundation\\Console\\Kernel->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Console\\Application))
#31 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(77): Illuminate\\Console\\Application->bootstrap()
#32 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(481): Illuminate\\Console\\Application->__construct(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Events\\Dispatcher), '10.48.29')
#33 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Illuminate\\Foundation\\Console\\Kernel->getArtisan()
#34 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-05-07 19:41:58] production.ERROR: PDOException: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php:65
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', Object(SensitiveParameterValue), Array)
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php(44): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\ConnectionFactory.php(184): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->{closure:Illuminate\Database\Connectors\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(1339): call_user_func(Object(Closure))
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(1375): Illuminate\Database\Connection->getPdo()
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(528): Illuminate\Database\Connection->getReadPdo()
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(423): Illuminate\Database\Connection->getPdoForSelect(true)
#9 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(816): Illuminate\Database\Connection->{closure:Illuminate\Database\Connection::select():414}('select `value` ...', Array)
#10 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#11 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select `value` ...', Array, Object(Closure))
#12 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select `value` ...', Array, true)
#13 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#14 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->{closure:Illuminate\Database\Query\Builder::get():2901}()
#15 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#16 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#17 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\DataRevision.php(36): Illuminate\Database\Query\Builder->first()
#18 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(71): App\Framework\src\Http\Models\DataRevision::getRevision()
#19 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(62): App\Framework\src\Http\Models\Setting::initSettingMap()
#20 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\functions.php(69): App\Framework\src\Http\Models\Setting::getSetting('enable_throttli...', '1')
#21 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Http\helpers.php(736): fS('enable_throttli...', true)
#22 S:\contentcreators\svn\2018\clients\sied\dop-2025\routes\web.php(336): throttleEnabled()
#23 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\RouteFileRegistrar.php(35): require('S:\\contentcreat...')
#24 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\RouteFileRegistrar->register('S:\\contentcreat...')
#25 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(465): Illuminate\Routing\Router->loadRoutes('S:\\contentcreat...')
#26 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(194): Illuminate\Routing\Router->group(Array, 'S:\\contentcreat...')
#27 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Providers\RouteServiceProvider.php(59): Illuminate\Routing\RouteRegistrar->group('S:\\contentcreat...')
#28 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Providers\RouteServiceProvider.php(44): App\Providers\RouteServiceProvider->mapWebRoutes()
#29 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): App\Providers\RouteServiceProvider->map()
#30 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::{closure:Illuminate\Container\BoundMethod::call():35}()
#31 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#32 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#33 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#34 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(122): Illuminate\Container\Container->call(Array)
#35 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(45): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#36 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Foundation\Support\Providers\RouteServiceProvider->{closure:Illuminate\Foundation\Support\Providers\RouteServiceProvider::register():39}()
#37 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::{closure:Illuminate\Container\BoundMethod::call():35}()
#38 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(81): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#39 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Object(Closure), Object(Closure))
#40 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Object(Closure), Array, NULL)
#41 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Support\ServiceProvider.php(119): Illuminate\Container\Container->call(Object(Closure))
#42 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1062): Illuminate\Support\ServiceProvider->callBootedCallbacks()
#43 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1040): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#44 [internal function]: Illuminate\Foundation\Application->{closure:Illuminate\Foundation\Application::boot():1039}(Object(App\Providers\RouteServiceProvider), 25)
#45 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1039): array_walk(Array, Object(Closure))
#46 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#47 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(286): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#48 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(186): Illuminate\Foundation\Application->bootstrapWith(Array)
#49 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(170): Illuminate\Foundation\Http\Kernel->bootstrap()
#50 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#51 S:\contentcreators\svn\2018\clients\sied\dop-2025\index.php(72): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#52 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `value` from `data_revision` where `key` = main.revision limit 1) in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php:829
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select `value` ...', Array, Object(Closure))
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select `value` ...', Array, true)
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->{closure:Illuminate\Database\Query\Builder::get():2901}()
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\DataRevision.php(36): Illuminate\Database\Query\Builder->first()
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(71): App\Framework\src\Http\Models\DataRevision::getRevision()
#9 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(62): App\Framework\src\Http\Models\Setting::initSettingMap()
#10 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\functions.php(69): App\Framework\src\Http\Models\Setting::getSetting('enable_throttli...', '1')
#11 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Http\helpers.php(736): fS('enable_throttli...', true)
#12 S:\contentcreators\svn\2018\clients\sied\dop-2025\routes\web.php(336): throttleEnabled()
#13 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\RouteFileRegistrar.php(35): require('S:\\contentcreat...')
#14 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(511): Illuminate\Routing\RouteFileRegistrar->register('S:\\contentcreat...')
#15 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(465): Illuminate\Routing\Router->loadRoutes('S:\\contentcreat...')
#16 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\RouteRegistrar.php(194): Illuminate\Routing\Router->group(Array, 'S:\\contentcreat...')
#17 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Providers\RouteServiceProvider.php(59): Illuminate\Routing\RouteRegistrar->group('S:\\contentcreat...')
#18 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Providers\RouteServiceProvider.php(44): App\Providers\RouteServiceProvider->mapWebRoutes()
#19 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): App\Providers\RouteServiceProvider->map()
#20 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::{closure:Illuminate\Container\BoundMethod::call():35}()
#21 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#22 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#23 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#24 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(122): Illuminate\Container\Container->call(Array)
#25 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Support\Providers\RouteServiceProvider.php(45): Illuminate\Foundation\Support\Providers\RouteServiceProvider->loadRoutes()
#26 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Foundation\Support\Providers\RouteServiceProvider->{closure:Illuminate\Foundation\Support\Providers\RouteServiceProvider::register():39}()
#27 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::{closure:Illuminate\Container\BoundMethod::call():35}()
#28 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(81): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#29 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Object(Closure), Object(Closure))
#30 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Object(Closure), Array, NULL)
#31 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Support\ServiceProvider.php(119): Illuminate\Container\Container->call(Object(Closure))
#32 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1062): Illuminate\Support\ServiceProvider->callBootedCallbacks()
#33 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1040): Illuminate\Foundation\Application->bootProvider(Object(App\Providers\RouteServiceProvider))
#34 [internal function]: Illuminate\Foundation\Application->{closure:Illuminate\Foundation\Application::boot():1039}(Object(App\Providers\RouteServiceProvider), 25)
#35 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1039): array_walk(Array, Object(Closure))
#36 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\BootProviders.php(17): Illuminate\Foundation\Application->boot()
#37 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(286): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(Illuminate\Foundation\Application))
#38 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(186): Illuminate\Foundation\Application->bootstrapWith(Array)
#39 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(170): Illuminate\Foundation\Http\Kernel->bootstrap()
#40 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#41 S:\contentcreators\svn\2018\clients\sied\dop-2025\index.php(72): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#42 {main}  
[2025-05-07 19:41:58] production.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `value` from `data_revision` where `key` = main.revision limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) (Connection: mysql, SQL: select `value` from `data_revision` where `key` = main.revision limit 1) at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `value` ...', Array, Object(Closure))
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `value` ...', Array, true)
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2901}()
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\DataRevision.php(36): Illuminate\\Database\\Query\\Builder->first()
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(71): App\\Framework\\src\\Http\\Models\\DataRevision::getRevision()
#9 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(62): App\\Framework\\src\\Http\\Models\\Setting::initSettingMap()
#10 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\functions.php(69): App\\Framework\\src\\Http\\Models\\Setting::getSetting('enable_throttli...', '1')
#11 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Http\\helpers.php(736): fS('enable_throttli...', true)
#12 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\routes\\web.php(336): throttleEnabled()
#13 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('S:\\\\contentcreat...')
#14 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('S:\\\\contentcreat...')
#15 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('S:\\\\contentcreat...')
#16 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'S:\\\\contentcreat...')
#17 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Providers\\RouteServiceProvider.php(59): Illuminate\\Routing\\RouteRegistrar->group('S:\\\\contentcreat...')
#18 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Providers\\RouteServiceProvider.php(44): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#19 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#20 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#21 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#25 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#26 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->{closure:Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider::register():39}()
#27 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#28 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#30 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#31 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#32 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#33 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#34 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1039}(Object(App\\Providers\\RouteServiceProvider), 25)
#35 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#36 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#37 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#38 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#39 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#40 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#41 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#42 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'forge'@'localhost' (using password: YES) at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'forge', Object(SensitiveParameterValue), Array)
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'forge', '', Array)
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select `value` ...', Array)
#10 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#11 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `value` ...', Array, Object(Closure))
#12 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `value` ...', Array, true)
#13 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2901}()
#15 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#17 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\DataRevision.php(36): Illuminate\\Database\\Query\\Builder->first()
#18 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(71): App\\Framework\\src\\Http\\Models\\DataRevision::getRevision()
#19 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(62): App\\Framework\\src\\Http\\Models\\Setting::initSettingMap()
#20 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\functions.php(69): App\\Framework\\src\\Http\\Models\\Setting::getSetting('enable_throttli...', '1')
#21 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Http\\helpers.php(736): fS('enable_throttli...', true)
#22 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\routes\\web.php(336): throttleEnabled()
#23 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('S:\\\\contentcreat...')
#24 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('S:\\\\contentcreat...')
#25 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('S:\\\\contentcreat...')
#26 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'S:\\\\contentcreat...')
#27 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Providers\\RouteServiceProvider.php(59): Illuminate\\Routing\\RouteRegistrar->group('S:\\\\contentcreat...')
#28 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Providers\\RouteServiceProvider.php(44): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#29 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#30 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#31 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#32 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#33 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#34 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#35 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#36 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->{closure:Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider::register():39}()
#37 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#38 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#40 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#41 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#42 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#43 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#44 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1039}(Object(App\\Providers\\RouteServiceProvider), 25)
#45 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#46 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#47 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#48 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#49 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#50 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#51 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#52 {main}
"} 
[2025-05-07 19:43:02] local.ERROR: PDOException: SQLSTATE[HY000] [1049] Unknown database 'dop_2025' in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php:65
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php(65): PDO->__construct('mysql:host=loca...', 'root', Object(SensitiveParameterValue), Array)
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php(44): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=loca...', 'root', '', Array)
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=loca...', Array, Array)
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\ConnectionFactory.php(184): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(1339): call_user_func(Object(Closure))
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(1375): Illuminate\Database\Connection->getPdo()
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(528): Illuminate\Database\Connection->getReadPdo()
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(423): Illuminate\Database\Connection->getPdoForSelect(true)
#9 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(816): Illuminate\Database\Connection->Illuminate\Database\{closure}('select `value` ...', Array)
#10 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#11 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select `value` ...', Array, Object(Closure))
#12 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select `value` ...', Array, true)
#13 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#14 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#15 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#16 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#17 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\DataRevision.php(36): Illuminate\Database\Query\Builder->first()
#18 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(71): App\Framework\src\Http\Models\DataRevision::getRevision()
#19 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(62): App\Framework\src\Http\Models\Setting::initSettingMap()
#20 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\functions.php(69): App\Framework\src\Http\Models\Setting::getSetting('extension.owner...', '66898008')
#21 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Console\Commands\DispatchChatInit.php(49): fS('extension.owner...', '66898008')
#22 [internal function]: App\Console\Commands\DispatchChatInit->__construct()
#23 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(952): ReflectionClass->newInstanceArgs(Array)
#24 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(795): Illuminate\Container\Container->build('App\\Console\\Com...')
#25 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(986): Illuminate\Container\Container->resolve('App\\Console\\Com...', Array, true)
#26 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(731): Illuminate\Foundation\Application->resolve('App\\Console\\Com...', Array)
#27 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(971): Illuminate\Container\Container->make('App\\Console\\Com...', Array)
#28 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(251): Illuminate\Foundation\Application->make('App\\Console\\Com...')
#29 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(349): Illuminate\Console\Application->resolve('App\\Console\\Com...')
#30 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(130): Illuminate\Foundation\Console\Kernel->Illuminate\Foundation\Console\{closure}(Object(Illuminate\Console\Application))
#31 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(77): Illuminate\Console\Application->bootstrap()
#32 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(481): Illuminate\Console\Application->__construct(Object(Illuminate\Foundation\Application), Object(Illuminate\Events\Dispatcher), '10.48.29')
#33 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Illuminate\Foundation\Console\Kernel->getArtisan()
#34 S:\contentcreators\svn\2018\clients\sied\dop-2025\artisan(34): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [1049] Unknown database 'dop_2025' (Connection: mysql, SQL: select `value` from `data_revision` where `key` = main.revision limit 1) in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php:829
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select `value` ...', Array, Object(Closure))
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select `value` ...', Array, true)
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\DataRevision.php(36): Illuminate\Database\Query\Builder->first()
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(71): App\Framework\src\Http\Models\DataRevision::getRevision()
#9 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Models\Setting.php(62): App\Framework\src\Http\Models\Setting::initSettingMap()
#10 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\functions.php(69): App\Framework\src\Http\Models\Setting::getSetting('extension.owner...', '66898008')
#11 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Console\Commands\DispatchChatInit.php(49): fS('extension.owner...', '66898008')
#12 [internal function]: App\Console\Commands\DispatchChatInit->__construct()
#13 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(952): ReflectionClass->newInstanceArgs(Array)
#14 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(795): Illuminate\Container\Container->build('App\\Console\\Com...')
#15 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(986): Illuminate\Container\Container->resolve('App\\Console\\Com...', Array, true)
#16 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Container\Container.php(731): Illuminate\Foundation\Application->resolve('App\\Console\\Com...', Array)
#17 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(971): Illuminate\Container\Container->make('App\\Console\\Com...', Array)
#18 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(251): Illuminate\Foundation\Application->make('App\\Console\\Com...')
#19 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(349): Illuminate\Console\Application->resolve('App\\Console\\Com...')
#20 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(130): Illuminate\Foundation\Console\Kernel->Illuminate\Foundation\Console\{closure}(Object(Illuminate\Console\Application))
#21 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Console\Application.php(77): Illuminate\Console\Application->bootstrap()
#22 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(481): Illuminate\Console\Application->__construct(Object(Illuminate\Foundation\Application), Object(Illuminate\Events\Dispatcher), '10.48.29')
#23 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Illuminate\Foundation\Console\Kernel->getArtisan()
#24 S:\contentcreators\svn\2018\clients\sied\dop-2025\artisan(34): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 {main}  
[2025-05-07 19:43:02] local.ERROR: SQLSTATE[HY000] [1049] Unknown database 'dop_2025' (Connection: mysql, SQL: select `value` from `data_revision` where `key` = main.revision limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'dop_2025' (Connection: mysql, SQL: select `value` from `data_revision` where `key` = main.revision limit 1) at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `value` ...', Array, Object(Closure))
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `value` ...', Array, true)
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\DataRevision.php(36): Illuminate\\Database\\Query\\Builder->first()
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(71): App\\Framework\\src\\Http\\Models\\DataRevision::getRevision()
#9 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(62): App\\Framework\\src\\Http\\Models\\Setting::initSettingMap()
#10 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\functions.php(69): App\\Framework\\src\\Http\\Models\\Setting::getSetting('extension.owner...', '66898008')
#11 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Console\\Commands\\DispatchChatInit.php(49): fS('extension.owner...', '66898008')
#12 [internal function]: App\\Console\\Commands\\DispatchChatInit->__construct()
#13 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): ReflectionClass->newInstanceArgs(Array)
#14 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Console\\\\Com...')
#15 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Console\\\\Com...', Array, true)
#16 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Console\\\\Com...', Array)
#17 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Console\\\\Com...', Array)
#18 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(251): Illuminate\\Foundation\\Application->make('App\\\\Console\\\\Com...')
#19 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(349): Illuminate\\Console\\Application->resolve('App\\\\Console\\\\Com...')
#20 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(130): Illuminate\\Foundation\\Console\\Kernel->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Console\\Application))
#21 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(77): Illuminate\\Console\\Application->bootstrap()
#22 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(481): Illuminate\\Console\\Application->__construct(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Events\\Dispatcher), '10.48.29')
#23 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Illuminate\\Foundation\\Console\\Kernel->getArtisan()
#24 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'dop_2025' at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=loca...', 'root', Object(SensitiveParameterValue), Array)
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=loca...', 'root', '', Array)
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=loca...', Array, Array)
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `value` ...', Array)
#10 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `value` ...', Array, Object(Closure))
#11 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `value` ...', Array, Object(Closure))
#12 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `value` ...', Array, true)
#13 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#14 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Query\\Builder->get(Array)
#17 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\DataRevision.php(36): Illuminate\\Database\\Query\\Builder->first()
#18 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(71): App\\Framework\\src\\Http\\Models\\DataRevision::getRevision()
#19 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Models\\Setting.php(62): App\\Framework\\src\\Http\\Models\\Setting::initSettingMap()
#20 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\functions.php(69): App\\Framework\\src\\Http\\Models\\Setting::getSetting('extension.owner...', '66898008')
#21 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Console\\Commands\\DispatchChatInit.php(49): fS('extension.owner...', '66898008')
#22 [internal function]: App\\Console\\Commands\\DispatchChatInit->__construct()
#23 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): ReflectionClass->newInstanceArgs(Array)
#24 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Console\\\\Com...')
#25 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Console\\\\Com...', Array, true)
#26 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Console\\\\Com...', Array)
#27 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Console\\\\Com...', Array)
#28 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(251): Illuminate\\Foundation\\Application->make('App\\\\Console\\\\Com...')
#29 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(349): Illuminate\\Console\\Application->resolve('App\\\\Console\\\\Com...')
#30 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(130): Illuminate\\Foundation\\Console\\Kernel->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Console\\Application))
#31 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(77): Illuminate\\Console\\Application->bootstrap()
#32 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(481): Illuminate\\Console\\Application->__construct(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Events\\Dispatcher), '10.48.29')
#33 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Illuminate\\Foundation\\Console\\Kernel->getArtisan()
#34 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-05-07 19:43:40] local.ERROR: PDOException: SQLSTATE[HY000] [1049] Unknown database 'dop_2025' in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php:65
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php(65): PDO->__construct('mysql:host=loca...', 'root', Object(SensitiveParameterValue), Array)
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\Connector.php(44): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=loca...', 'root', '', Array)
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=loca...', Array, Array)
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connectors\ConnectionFactory.php(184): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->{closure:Illuminate\Database\Connectors\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(1339): call_user_func(Object(Closure))
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(528): Illuminate\Database\Connection->getPdo()
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(423): Illuminate\Database\Connection->getPdoForSelect(false)
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(816): Illuminate\Database\Connection->{closure:Illuminate\Database\Connection::select():414}('select table_na...', Array)
#9 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#10 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select table_na...', Array, Object(Closure))
#11 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(401): Illuminate\Database\Connection->select('select table_na...', Array, false)
#12 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Schema\MySqlBuilder.php(41): Illuminate\Database\Connection->selectFromWriteConnection('select table_na...')
#13 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Schema\Builder.php(165): Illuminate\Database\Schema\MySqlBuilder->getTables(false)
#14 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(355): Illuminate\Database\Schema\Builder->hasTable('admins')
#15 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Traits\ManageableModelTrait.php(79): Illuminate\Support\Facades\Facade::__callStatic('hasTable', Array)
#16 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Http\Controllers\Deploy\DeploymentController.php(63): App\Framework\src\Http\Models\Admin->migrateSchema()
#17 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Deploy\DeploymentController->migrate(Object(Illuminate\Http\Request))
#18 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('migrate', Array)
#19 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Deploy\DeploymentController), 'migrate')
#20 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#21 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#22 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->{closure:Illuminate\Routing\Router::runRouteWithinStack():805}(Object(Illuminate\Http\Request))
#23 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Http\Middleware\DeploymentToken.php(20): Illuminate\Pipeline\Pipeline->{closure:Illuminate\Pipeline\Pipeline::prepareDestination():142}(Object(Illuminate\Http\Request))
#24 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\DeploymentToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#26 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#27 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#28 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#29 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#30 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#31 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->{closure:Illuminate\Foundation\Http\Kernel::dispatchToRouter():197}(Object(Illuminate\Http\Request))
#32 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Http\Middleware\CheckForMaintenanceMode.php(45): Illuminate\Pipeline\Pipeline->{closure:Illuminate\Pipeline\Pipeline::prepareDestination():142}(Object(Illuminate\Http\Request))
#33 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#34 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\spatie\laravel-csp\src\AddCspHeaders.php(13): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#35 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Spatie\Csp\AddCspHeaders->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#37 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#40 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#43 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#45 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#47 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#49 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#50 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#51 S:\contentcreators\svn\2018\clients\sied\dop-2025\index.php(72): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#52 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [1049] Unknown database 'dop_2025' (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'dop_2025' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) in S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php:829
Stack trace:
#0 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select table_na...', Array, Object(Closure))
#2 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Connection.php(401): Illuminate\Database\Connection->select('select table_na...', Array, false)
#3 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Schema\MySqlBuilder.php(41): Illuminate\Database\Connection->selectFromWriteConnection('select table_na...')
#4 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Database\Schema\Builder.php(165): Illuminate\Database\Schema\MySqlBuilder->getTables(false)
#5 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(355): Illuminate\Database\Schema\Builder->hasTable('admins')
#6 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Framework\src\Http\Traits\ManageableModelTrait.php(79): Illuminate\Support\Facades\Facade::__callStatic('hasTable', Array)
#7 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Http\Controllers\Deploy\DeploymentController.php(63): App\Framework\src\Http\Models\Admin->migrateSchema()
#8 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Deploy\DeploymentController->migrate(Object(Illuminate\Http\Request))
#9 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('migrate', Array)
#10 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Deploy\DeploymentController), 'migrate')
#11 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#12 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#13 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->{closure:Illuminate\Routing\Router::runRouteWithinStack():805}(Object(Illuminate\Http\Request))
#14 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Http\Middleware\DeploymentToken.php(20): Illuminate\Pipeline\Pipeline->{closure:Illuminate\Pipeline\Pipeline::prepareDestination():142}(Object(Illuminate\Http\Request))
#15 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\DeploymentToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#17 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#18 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#19 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#20 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#21 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#22 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->{closure:Illuminate\Foundation\Http\Kernel::dispatchToRouter():197}(Object(Illuminate\Http\Request))
#23 S:\contentcreators\svn\2018\clients\sied\dop-2025\app\Http\Middleware\CheckForMaintenanceMode.php(45): Illuminate\Pipeline\Pipeline->{closure:Illuminate\Pipeline\Pipeline::prepareDestination():142}(Object(Illuminate\Http\Request))
#24 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): App\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\spatie\laravel-csp\src\AddCspHeaders.php(13): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#26 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Spatie\Csp\AddCspHeaders->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#28 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#31 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#32 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#34 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#35 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#36 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#37 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#38 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->{closure:{closure:Illuminate\Pipeline\Pipeline::carry():158}:159}(Object(Illuminate\Http\Request))
#40 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#41 S:\contentcreators\svn\2018\clients\sied\dop-2025\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#42 S:\contentcreators\svn\2018\clients\sied\dop-2025\index.php(72): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#43 {main}  
[2025-05-07 19:43:40] local.ERROR: SQLSTATE[HY000] [1049] Unknown database 'dop_2025' (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'dop_2025' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'dop_2025' (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'dop_2025' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#4 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->hasTable('admins')
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Traits\\ManageableModelTrait.php(79): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Http\\Controllers\\Deploy\\DeploymentController.php(63): App\\Framework\\src\\Http\\Models\\Admin->migrateSchema()
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Deploy\\DeploymentController->migrate(Object(Illuminate\\Http\\Request))
#9 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('migrate', Array)
#10 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Deploy\\DeploymentController), 'migrate')
#11 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#14 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Http\\Middleware\\DeploymentToken.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#15 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\DeploymentToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#17 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#23 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Http\\Middleware\\CheckForMaintenanceMode.php(45): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#24 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\spatie\\laravel-csp\\src\\AddCspHeaders.php(13): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#26 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\Csp\\AddCspHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#34 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#40 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'dop_2025' at S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=loca...', 'root', Object(SensitiveParameterValue), Array)
#1 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=loca...', 'root', '', Array)
#2 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=loca...', Array, Array)
#3 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#7 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select table_na...', Array)
#9 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#10 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#11 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#12 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#13 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#14 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->hasTable('admins')
#15 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Framework\\src\\Http\\Traits\\ManageableModelTrait.php(79): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#16 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Http\\Controllers\\Deploy\\DeploymentController.php(63): App\\Framework\\src\\Http\\Models\\Admin->migrateSchema()
#17 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Deploy\\DeploymentController->migrate(Object(Illuminate\\Http\\Request))
#18 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('migrate', Array)
#19 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Deploy\\DeploymentController), 'migrate')
#20 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#21 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#22 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#23 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Http\\Middleware\\DeploymentToken.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#24 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\DeploymentToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#26 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#32 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\app\\Http\\Middleware\\CheckForMaintenanceMode.php(45): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#33 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\spatie\\laravel-csp\\src\\AddCspHeaders.php(13): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#35 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Spatie\\Csp\\AddCspHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#37 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#40 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#43 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#45 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#47 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#49 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#51 S:\\contentcreators\\svn\\2018\\clients\\sied\\dop-2025\\index.php(72): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#52 {main}
"} 
[2025-05-07 19:44:22] local_2.DEBUG: App\Code\PsnApi::handleTokenAuth() - array (
  'accessToken' => '4aec4e6f-8224-436b-a796-5015a7511114',
  'refreshToken' => NULL,
  'expiresIn' => 3599,
)  
[2025-05-07 19:44:23] local_2.ERROR: PsnApiController::savePsnUserData - 'SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`sied_dop_2025`.`tickets`, CONSTRAINT `tickets_transaction_id_foreign` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`id`)) (Connection: mysql, SQL: insert into `tickets` (`label`, `ticket`, `user_id`, `identifier`, `transaction_id`, `type`, `created_at`, `updated_at`) values ({"[TICKETS]":3}, 3, 1, initial-trophies, , trophy-game, 2025-05-07 19:44:23, 2025-05-07 19:44:23))'  
