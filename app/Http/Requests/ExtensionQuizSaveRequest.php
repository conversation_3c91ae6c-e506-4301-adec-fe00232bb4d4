<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ExtensionQuizSaveRequest extends FormRequest{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(){
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(){
        
        $rules = [
            'quiz_name'      => 'required',
            'question.*'       => 'required',
            //'correct_answer' => 'required',
            //'answer2'      => 'required',
            //'answer3'      => 'required',
            //'answer4'      => 'required',
        ];

        return $rules;
    }

    /**
     * Custom message for validation
     *
     * @return array
     */
    public function messages()  {

        return [
            
        ];
    }
}
