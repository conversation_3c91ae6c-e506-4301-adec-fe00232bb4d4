<a href="#" class="btn btn-white btn-icon offcanvas-btn-close cc-adobe-track" {!! adobeTrackData('close click', "math riddle", "1", "icon") !!} data-bs-dismiss="offcanvas" aria-label="Close"><i class="cc_icon-close"><svg role="img"><title>Close icon</title><use xlink:href="#cc_icon-close"></use></svg></i></a>
<div class="offcanvas-body">
    <div class="cc-offcanvas-header text-a">
        <h2 class="h2" id="cc-math-riddleLabel">{!! $mathRiddle->math_riddle_title !!}</h2>
    </div>
    <div class="cc-offcanvas-body">
        @if($mathRiddle)
            <div class="cc-game-thumb math-riddle ratio {{ ($ticket) ? 'done' : '' }}">
                <img decoding="async" loading="lazy" src="{{ ccImgUrl($mathRiddle->riddle_image, "math-riddle-cta.jpg") }}" alt="Torem ipsum dolor sit amet, consectetur adipiscing elit.">
                @if($ticket)
                    @php
                        $icon = ($ticket->tickets > 0) ? 'check' : 'close';
                    @endphp
                    <span class="cc_mg_game-done-icon"><i class="cc_icon-{{$icon}}-2"><svg role="img"><title>{{$icon}}-2 icon</title><use xlink:href="#cc_icon-{{$icon}}-2"></use></svg></i></span>
                @endif
            </div>
            @if(!$ticket)
                <form action="#" name="math-riddle-form" method="post" class="math-riddle-form">
                    <div class="cc-math-riddle-form-wrap">
                        <div class="cc-magic-spot-row">
                            <span class="cc-magic-spot-eype">
                                <i class="cc_icon-calc"><svg role="img"><title>Calc icon</title><use xlink:href="#cc_icon-calc"></use></svg></i>
                            </span>
                            <span class="cc-magic-spot-eypetext h3">{!! fT('d_pages.c_math_riddle.a_code_eingeben', 'Code eingeben:') !!}</span>
                        </div>
                        <div class="cc-magic-redeem-form-row">
                            <div class="cc-magic-redeem-form-lcell">
                                <div class="form-group mb-0">
                                    <label class="visually-hidden" for="cc-answer-code">Enter answer</label>
                                    <div class="form-control-wrapper">
                                        <input type="text" name="answer_code" class="form-control" placeholder="{{ fT('d_pages.c_math_riddle.b_enter_answer', 'Enter answer') }}" autocomplete="off" id="cc-answer-code">
                                        <span class="invalid-feedback"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="cc-magic-redeem-form-rcell">
                                <div class="btn-spacer">
                                    <button type="submit" name="btn-submit" class="btn btn-white cc-math-riddle-submit-btn cc-adobe-track" {!! adobeTrackData('submit click', "math riddle", "2", "button") !!}><span class="btn-text">{!! fT('d_pages.c_math_riddle.c_reedem_btn_text', 'Reedem') !!}</span></button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="code" value="{{ $mathRiddle->code }}" />
                    {{ csrf_field() }}
                </form>
            @endif
        @endif            
    </div>

    <div class="cc-bt-description text-a">
        {!! auto_ptag(fT('d_pages.c_math_riddle.d_description', '<p>Torem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis.</p>')) !!}
    </div>
</div>