<?php
namespace App\Http\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Framework\src\Http\Traits\ManageableModelTrait;
use App\Framework\src\Http\Traits\EncryptableTrait;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * @property integer id
 * @property integer user_id
 * @property integer competition_id
 * @property integer tickets
 * @property string  prize_type
 * @property string  instant_code
 * @property integer is_winner
 * @property boolean is_notified
 
 * @property string  created_at
 * @property string  updated_at
 *
 * @mixin Builder
 * @mixin Eloquent
 */
class Participant extends Model{
	use ManageableModelTrait;
    use EncryptableTrait;
	
    protected $table       = 'participants';
    protected $guarded     = [];
    protected $encryptable = [];
	
    protected static $NON_ID_FIELDS = [
        
        "user_id"           => ["type" => "big_integer", "unsigned" => true, "foreign" => ["table" => "psn_users", "field" => "id"]],
        "competition_id"    => ["type" => "big_integer", "unsigned" => true, "foreign" => ["table" => "competitions", "field" => "id"]],
        "tickets"           => ["type" => "integer", "default" => 0, 'index' => true],
        "prize_type"        => ["type" => "string", 'length' => 50, "default" => null], //main or runner_up
        "instant_code"      => ["type" => "string", 'length' => 50, "default" => null],
        "is_winner"         => ["type" => "bool", "default" => 0],
        "is_notified"       => ["type" => "boolean", "default" => 0],
        
        // ---
        "created_at"   => ["type" => "created_at", "null" => true],
        "updated_at"   => ["type" => "updated_at"],
    ];


    public static function drawWinner($draw_day, $priceType){

        $currentTime = current_time();
        $active_day = get_calendar_date();

        if($active_day != $draw_day && strtotime($active_day." 00:00:00") > strtotime($draw_day." 00:00:00"))  {

            // Debug
            Log::debug(sprintf("%s() - Check Today: %s bigger than ParticipantGroup: %s", shortClassMethod(__METHOD__),
                date("Y-m-d", $currentTime), date("Y-m-d", strtotime($draw_day))));

            $competition = static::priceDefinedForDay($draw_day, $priceType);
            if(!$competition)
                return $priceType." prize is not defined for the day ".$draw_day;

            $winnerCountForPrice = $priceType == "main" ? 1 : 10;

            if(static::amountPriceWinnerForDay($competition->id, $priceType) < $winnerCountForPrice)  {

                if(static::participantsWithoutPrice($competition->id))  {

                    // Debug
                    Log::debug(sprintf("%s() - Drawing winners now...", shortClassMethod(__METHOD__)));

                    $returnParticipant = static::drawWinnerForType($competition, $draw_day, $priceType);

                    // Debug
                    if($returnParticipant) {
                        Log::debug(sprintf("%s() - New winner ID=%s for day=%s, price_type=%s", shortClassMethod(__METHOD__),
                        $returnParticipant->id, $returnParticipant->day, $returnParticipant->prize_type));
                    }

                    return true;
                }
                else  {

                    return "There are no new participants to draw as winners!";
                }
            }
            else  {

                return "All winner for ".$priceType." prize type is drawn!";
            }
        }
        else  {
            return "You can not draw winner before draw day is completed! Please activate new day first.";
        }
    }

    /**
     * @param false  $date
     * @param string $priceType
     *
     * @return bool|Competition|null
     */
    public static function priceDefinedForDay($date, $priceType){

        // Validate input
        if(!in_array($priceType, ["main", "follow_up"])) return false;

        // Find price for the day
        return Competition::where('date', $date)->first();
    }


    /**
     * @param false  $competition_id
     * @param string $priceType
     *
     * @return int|null
     */
    public static function amountPriceWinnerForDay($competition_id, $priceType = ""){

        // Validate input
        if(!in_array($priceType, ["main", "follow_up", "any"])) return null;

        // Fetch participants for day
        $whereData = [
            ["competition_id", "=", $competition_id],
            ["is_winner", "=", 1],
        ];

        if($priceType != "any")
            $whereData[] = ["prize_type", "=", $priceType];

        return Participant::join("psn_users", "psn_users.id", "participants.user_id")
                ->where($whereData)->whereIn('country_code', ["DE", "AT", "CH"])
                ->count();
    }

    /**
     * @param $competition_id
     *
     * @return int
     */
    public static function participantsWithoutPrice($competition_id){

		$past_winner_ids = static::last_7day_winner_user_ids();

        pr($past_winner_ids);

        // Fetch participants for day
        $whereData = [
            ["competition_id", "=", $competition_id],
            ["tickets", ">", 0],
            ["is_winner", "=", 0],
            ["tickets", "<", 2001],
            ["fake_user", "=", 0],
        ];

        return Participant::join("psn_users", "psn_users.id", "participants.user_id")
                    ->where($whereData)
                    ->whereIn('country_code', ["DE", "AT", "CH"])
                    ->whereNotIn('participants.user_id', $past_winner_ids)
                    ->count();
    }


	public static function last_7day_winner_user_ids() {

		//get winner user ids who win on last 10 days
		$past_win_date = Carbon::createFromTimestamp(current_time())->addDays(-7)->format("Y-m-d");

		$past_10day_winners = Participant::select('participants.user_id')
                                ->join("psn_users", "psn_users.id", "participants.user_id")
                                ->whereRaw("is_winner > 0 AND participants.updated_at >= ?", $past_win_date)
                                ->whereIn('country_code', ["DE", "AT", "CH"])
                                ->get();

		$past_winner_ids = array();
		if($past_10day_winners->count() > 0)  {
			foreach($past_10day_winners as $pastWinner)  {
				$past_winner_ids[$pastWinner->user_id] = $pastWinner->user_id;
			}
		}

		return $past_winner_ids;
	}

    /**
     * @param false  $time
     * @param string $priceType
     *
     * @return Participants|mixed|null
     */
    public static function drawWinnerForType1($competition, $date, $priceType)  { // main or follow_up

        // Validate input
        if(!in_array($priceType, ["main", "follow_up"])) return null;

        // Check if amount prices already reached
        if($priceType == "main" && static::amountPriceWinnerForDay($date, "main") >= 1) return null;
        if($priceType == "follow_up" && static::amountPriceWinnerForDay($date, "follow_up") >= 10) return null;
        if(static::amountPriceWinnerForDay($date, "any") >= 4) return null;
        if(!static::priceDefinedForDay($date, $priceType)) return null;

		$past_winner_ids = static::last_10day_winner_user_ids();
        $excludedUserIds = static::getExcludedUserIds();
        $excludedUserIdsSecond = static::getExcludedUserIdsSecond();
        $excludedUserIdsStatic = Participants::getExcludedUserIdsStatic();
        $excludeIds = array_merge(array_values($past_winner_ids), $excludedUserIds, $excludedUserIdsSecond, $excludedUserIdsStatic);

        // Fetch participants for day
        $whereData    = [
            ["day", "=", $date],
            ["tickets", ">", 0],
            ["is_winner", "=", 0],
            ["tickets", "<", 32001],
            ["fake_user", "=", 0],
        ];

        $participants = Participants::selectRaw("participants.*")
                            ->join("psn_users", "psn_users.id", "participants.user_id")
                            ->where($whereData)
                            ->whereNotIn('participants.user_id', $excludeIds)
                            ->whereIn('country_code', ["DE", "AT", "CH"])
                            ->inRandomOrder()->get();

        $totalTickets = Participants::join("psn_users", "psn_users.id", "participants.user_id")
                            ->where($whereData)
                            ->whereNotIn('participants.user_id', $excludeIds)
                            ->whereIn('country_code', ["DE", "AT", "CH"])
                            ->sum("tickets");

        $totalTickets = $totalTickets ?: 0;

        // Determine winner ticket index
        $winnerTicketNum  = mt_rand(0, $totalTickets);
        $currentTicketNum = 0;

        // Find winner
        $winnerParticipant = null;
        foreach($participants as $participant){
            $newTicketNum = $currentTicketNum + $participant->tickets;
            if($winnerTicketNum <= $newTicketNum && $winnerTicketNum >= $currentTicketNum){
                // Debug
                Log::debug(sprintf("%s() - userTicketOffsetMin=%s, userTicketOffsetMax=%s, winnerTicketOffset=%s, globalTicketAmount=%s, participantId=%s, type=%s, day=%s",
                    shortClassMethod(__METHOD__), $currentTicketNum, $newTicketNum, $winnerTicketNum,
                    $totalTickets, $participant->id, $priceType, $participant->day));

                $winnerParticipant = $participant;
                break;
            }
            $currentTicketNum = $newTicketNum;
        }

        // Check no winner found
        if(!$winnerParticipant) return $winnerParticipant;

        $price = static::getDefinedPriceForDay($winnerParticipant->day, $priceType);

        // Mark winner
        $winnerParticipant->is_winner       = 1;
        $winnerParticipant->prize_type      = $priceType;
        $participant->winner_data           = $price->name;
        //$winnerParticipant->claim_code      = generateRandomString(20);
        //$winnerParticipant->form_type       = $price->form_type;
        $winnerParticipant->claim_last_date = Carbon::createFromTimestamp(time())->addDays(7)->format("Y-m-d");
        $winnerParticipant->save();

        /*
        // Send push notification
        $user  = PsnUsers::selectRaw('psn_users.*, '. AESdecypt('email'))->where("id", $winnerParticipant->user_id)->first();

        if($price && $user)  {

            try{
                static::sendWinnerNotification($user, $price, $winnerParticipant);
            }
            catch(\Exception $e){
                Log::error($e->getMessage());
            }
        }
        */

        return $winnerParticipant;
    }
}