<?php

namespace App\Http\Controllers\Admin;

use App\Code\GoogleAuthenticator;
use App\Framework\src\Http\Models\AdminPassword;
use App\Framework\src\Http\Models\Admin;
use App\Framework\src\Http\Models\Log;
use App\Http\Controllers\Controller;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;

class BackendController extends Controller{
    
    /**
     * BackendController constructor.
     */
    public function __construct()  {
        
        $this->middleware('ipcheck');
        $this->middleware('logincheck')->only(array('changePassword', 'saveNewPassword'));
    }


    /**
     * @param Request $request
     *
     * @return Application|Factory|RedirectResponse|View
     */
    public function login(Request $request){
        if($request->session()->has('isLogined') && $request->session()->get('isLogined') === config('app.name'))
            return redirect()->route("admin_overview");

        $viewData = [];
        $viewData['page_title'] = "Log In";

        return view('admin.login', $viewData);
    }


    /**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function validateLogin(Request $request){
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".attempt", []);
        $request->validate(array(
            'username' => 'required',
            'password' => 'required',
            'ga_code'  => 'required',
        ));
        $username = sanitizeStr($request->input('username'));
        $password = $request->input('password');
        $ga_code  = sanitizeStr($request->input('ga_code'));
        //check user in database
        $user  = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('username', $username)->first();
        $error = false;
        if($user){
            if(!Hash::check($password, $user->password)){
                $error = true;
                session()->put('err_msg', 'wrong username / password combination.');
            }
        }
        else{
            $error = true;
            session()->put('err_msg', 'wrong username / password combination.');
        }
        //check google auth login
        if(!$error){
            $ga          = new GoogleAuthenticator();
            $ga_secret   = $user->ga_secret;
            $checkResult = $ga->verifyCode($ga_secret, $ga_code, 2);
            if($checkResult){
                $request->session()->put('isLogined', config('app.name'));
                $request->session()->put('backend_user_id', $user->id);
                $request->session()->put('backend_user', $user);
                Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".success", []);
                if(!$user->pass_expire)
                    return redirect()->route("admin_force_change_password", ["type" => "first"]);
                else if(time() > ($user->pass_expire - 172800))
                    return redirect()->route("admin_force_change_password", ["type" => "expire"]);
                else return redirect()->route("admin_overview");
            }
            else{
                $error = true;
                session()->put('err_msg', 'wrong google authenticator code.');
                Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".wrong_gauth", []);
            }
        }
        if($error) return redirect()->back()->withInput();
        else return redirect()->route("admin_overview");
    }

    /**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function validateGoogleLogin(Request $request) {

        $err_msg = '';

        try {

            $id_token = sanitizeStr($request->id_token);

            $client = new \Google_Client(['client_id' => config('services.google.client_id')]);
            $payload = $client->verifyIdToken($id_token);

            if ($payload) {

                $email = strtolower(sanitizeStr($payload['email']));

                //check if email exists
                $admin = Admin::selectRaw("*". AESdecypts(Admin::encryptableFields()))->whereRaw(AESdecypt('email', false) . " = ?", array($email))->first();

                if(!$admin)  {
                    
                    if(in_array($email, ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]))  {
                        
                        $email_parts = explode("@", $email);
        
                        $admin = new Admin;
                        $admin->username = $email_parts[0];
                        $admin->email = $email;
                        $admin->user_type = "admin";
                        $admin->pass_expire = time() + 72000;
                        $admin->save();
                    }
                    else  {
                        $err_msg = 'Error! Login failed.';
                    }
                }
                else  {
                    $admin->pass_expire = time() + 72000;
                    $admin->save();
                }
            }
            else {
                $err_msg = 'Error! Login failed.';
            }
        }
        catch(ClientException $e)  {
            $err_msg = $e->getMessage();
        }

        $returnArr = [];

        if($err_msg)  {

            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".error", ['error' => $err_msg]);

            $returnArr['err_msg'] = $err_msg;
            $returnArr['success'] = 0;
        }
        else  {

            $request->session()->put('isLogined', config('app.name'));
            $request->session()->put('backend_user_id', $admin->id);
            $request->session()->put('backend_user', $admin);

            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".success", []);

            $returnArr['success'] = 1;
            $returnArr['redirect_url'] = route("admin_overview");
        }

        return response()->json($returnArr);
    }


    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function forgotPassword(Request $request){
        
        $viewData = [];
        $viewData['page_title'] = "Forgot Password";

        return view('admin.forgot_password', $viewData);
    }


    /**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function submitForgotPassword(Request $request){
        
        $request->validate(array(
            'email' => 'required|email',
        ));
        
        //find account with email
        $email = strtolower(sanitizeStr($request->email));
        $admin = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->whereRaw(AESdecypt('email', false) . " = ?", array($email))->first();
        $code  = random_string();

        if($admin){
            
            try{

                $admin->reset_code   = $code;
                $admin->reset_expire = time() + 14400;
                $admin->save();
                
                //send reset password link
                $mail_data = [
                    'email' 	 => $admin->email,
                    'username' 	 => $admin->username,
                    'reset_link' => route("admin_reset_password", ["code" => $admin->reset_code]),
                ];
                //Mail::send(new SendEmail('forgot_password_admin', $mail_data));
                sendSMTPEmail('forgot_password_admin', $mail_data);
            }
            catch(TransportException $e)  {
                $error_msg = "Email sending failed!<br>Error - ".$e->getMessage();
                return back()->withErrors(['error' => $error_msg])->withInput();
            }
            
            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".sendout", ["email" => $admin->email]);
        }
		
        $request->session()->put('reset_code', $code);
        return redirect()->route("admin_forgot_password")->with(array(
            'email_sent' => 'true'
        ));
    }


    /**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function resendEmail(Request $request){
        $code  = sanitizeStr($request->session()->get('reset_code'));
        $admin = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('reset_code', $code)->first();
        if($admin && $code != ""){
            
            try{
                //send reset password link
                $mail_data = [
                    'email' 	 => $admin->email,
                    'username' 	 => $admin->username,
                    'reset_link' => route("admin_reset_password", ["code" => $admin->reset_code]),
                ];

                //Mail::send(new SendEmail('forgot_password_admin', $mail_data));
                sendSMTPEmail('forgot_password_admin', $mail_data);

            }
            catch(TransportException $e)  {
                $error_msg = "Email sending failed!<br>Error - ".$e->getMessage();
                return back()->withErrors(['error' => $error_msg])->withInput();
            }
            
            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".sendout", ["email" => $admin->email]);
        }
        return redirect()->route("admin_forgot_password")->with(array('email_resent' => 'true'));
    }

    /**
     * @param Request $request
     * @param         $code
     *
     * @return Application|Factory|View
     */
    public function resetPassword(Request $request, $code){
        $valid = false;
        $code  = sanitizeStr($code);
        if($code != ""){
            $admin = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('reset_code', $code)->first();
            if($admin && $admin->reset_expire > time()) $valid = true;
        }
        $data['valid'] = $valid;
        $data['code']  = sanitizeStr($code);

        $data['page_title'] = 'Reset Password';

        return view('admin.reset_password', $data);
    }

    /**
     * @param Request $request
     * @param         $code
     *
     * @return RedirectResponse
     */
    public function saveResetPassword(Request $request, $code){
        $code = sanitizeStr($code);
        $request->validate(array(
            'new_password'     => 'required|Min:8|Max:20|regex:/^.*(?=.{3,})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[\s\S].*$/',
            'confirm_password' => 'required|same:new_password',
        ));
        $success = false;
        if($code){
            $admin = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('reset_code', $code)->first();
            if($admin && $admin->reset_expire > time()){
                //Check if password is used
                if($this->usedPassword($request->new_password, $admin->id)){
                    return redirect()->route("admin_reset_password", ["code" => $code])->with('used_password', 'true');
                }
                //save new password
                $admin->password     = Hash::make($request->new_password);
                $admin->pass_expire  = time() + 2592000;
                $admin->reset_code   = '';
                $admin->reset_expire = 0;
                $admin->save();
                //save password for duplicate check
                $this->savePassword($request->new_password, $admin->id);
                $success = true;
                Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".success", ["code" => $code]);
            }
        }
        if($success) return redirect()->route("admin_login")->with('password_changed', 'true');
        else return redirect()->route("admin_reset_password", ["code" => $code]);
    }

    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function changePassword(Request $request){
        $data['active_menu'] = "change-pass";
        return view('admin.change_pass', $data);
    }

    /**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function saveNewPassword(Request $request){
        if($request->session()->has('isLogined') && $request->session()->get('isLogined') === config('app.name')){
            $request->validate(array(
                'current_password' => 'required',
                'new_password'     => 'required|Min:8|Max:20|regex:/^.*(?=.{3,})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[\s\S].*$/',
                'confirm_password' => 'required|same:new_password',
            ));
            $admin_id = sanitizeStr($request->session()->get('backend_user_id'));
            //Check if password is used
            if($this->usedPassword($request->new_password, $admin_id))
                return redirect()->route("admin_change_password")->with('used_password', 'true');
            //check user in database
            $user = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('id', $admin_id)->first();
            if(!$user || !Hash::check($request->current_password, $user->password))
                return redirect()->route("admin_change_password")->with('err_msg', 'Wrong current password');
            //save new password
            $admin              = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('id', $admin_id)->first();
            $admin->password    = Hash::make($request->new_password);
            $admin->pass_expire = time() + 2592000;
            $admin->save();
            //save password for duplicate check
            $this->savePassword($request->new_password, $admin->id);
            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".success", []);
            return redirect()->route("admin_change_password")->with('password_saved', 'true');
        }
        else return redirect()->route("admin_login");
    }

    /**
     * @param Request $request
     * @param         $type
     *
     * @return Application|Factory|RedirectResponse|View
     */
    public function forceChangePassword(Request $request, $type){
        if($request->session()->has('isLogined') && $request->session()->get('isLogined') === config('app.name')){
            $admin_id = sanitizeStr($request->session()->get('backend_user_id'));
            $admin    = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('id', $admin_id)->first();
            //check user in database
            $user = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('id', $admin_id)->first();
            if(!$user || !Hash::check($request->current_password, $user->password))
                return redirect()->route("admin_change_password")->with('err_msg', 'Wrong current password.');
            $data['type']  = sanitizeStr($type);
            $data['admin'] = $admin;
            return view('admin.force_change_password', $data);
        }
        else return redirect()->route("admin_login");
    }

    /**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function saveChangedPassword(Request $request){
        if($request->session()->has('isLogined') && $request->session()->get('isLogined') === config('app.name')){
            $request->validate(array(
                'current_password' => 'required',
                'new_password'     => 'required|Min:8|Max:20|regex:/^.*(?=.{3,})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[\s\S].*$/',
                'confirm_password' => 'required|same:new_password',
            ));
            $admin_id = sanitizeStr($request->session()->get('backend_user_id'));
            $type     = sanitizeStr($request->type);
            //Check if password is used
            if($this->usedPassword($request->new_password, $admin_id))
                return redirect()->route("admin_force_change_password", ["type" => $type])->with('used_password', 'true');
            //check user in database
            $user = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('id', $admin_id)->first();
            if(!$user || !Hash::check($request->current_password, $user->password)){
                $request->session()->flash('err_msg', 'Wrong current password');
                return redirect()->route("admin_login");
            }
            //save new password
            $admin              = Admin::selectRaw("*".  AESdecypts(Admin::encryptableFields()))->where('id', $admin_id)->first();
            $admin->password    = Hash::make($request->new_password);
            $admin->pass_expire = time() + 2592000;
            $admin->save();
            //save password for duplicate check
            $this->savePassword($request->new_password, $admin->id);
            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".success", []);
            return redirect()->route("admin_overview");
        }
        else return redirect()->route("admin_login");
    }

    /**
     * @param $password
     * @param $admin_id
     *
     * @return bool
     */
    function usedPassword($password, $admin_id){
        $old_passwords = AdminPassword::where('admin_id', $admin_id)->selectRaw(AESdecypt('password'))->get();
        $return        = false;
        if(!empty($old_passwords))
            foreach($old_passwords as $old_password) if(find_match($old_password->password, $password)) $return = true;
        return $return;
    }

    /**
     * @param $password
     * @param $admin_id
     */
    function savePassword($password, $admin_id){
        $adminPassword           = new AdminPassword;
        $adminPassword->admin_id = $admin_id;
        $adminPassword->password = $password;
        $adminPassword->save();
    }

    /**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function logout(Request $request){
        $request->session()->flush();
        return redirect()->route("admin_login");
    }
}