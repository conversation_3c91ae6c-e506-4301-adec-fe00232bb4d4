@extends('admin.layouts.layout')

@section('content')

    @section('custom_css')
        <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/select2.min.css">
        <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/select2-theme.min.css">
        <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/datepicker.min.css">
    @endsection


@php
    
    if($module)  {
        $mail_inputs = array();
    }
    else {
        $mail_inputs = array(
            array(
                'heading' => 'Forgot Passowrd (Admin User)',
                'name' => 'forgot_password_admin',
                'comment' => 'Mail to admin user for reset the password',
                'fields' => '[email], [username], [reset_link]'
            ),
        );
    }
@endphp
    
    <div class="nxl-content without-header nxl-full-content">
        
        @if(!empty($tabs) || $active_menu == "app_emails")

            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">Email Management</h5>
                    </div>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex d-md-none">
                            <a href="javascript:void(0)" class="page-header-right-close-toggle">
                                <i class="feather-arrow-left me-2"></i>
                                <span>Back</span>
                            </a>
                        </div>
                        
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            
                            <a href="{{ route('admin_export_texts', ['module' => $module]) }}" class="btn btn-info">
                                <i class="feather-download me-2"></i>
                                <span>Export Text</span>
                            </a>

                            <a href="javascript:;" data-bs-toggle="modal" data-bs-target="#import_modal" class="btn btn-primary">
                                <i class="feather-upload me-2"></i>
                                <span>Import Text</span>
                            </a>
                            
                        </div>

                    </div>
                    <div class="d-md-none d-flex align-items-center">
                        <a href="javascript:void(0)" class="page-header-right-open-toggle">
                            <i class="feather-align-right fs-20"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- [ Main Content ] start -->
            <div class="main-content d-flex">
                
                <!-- [ Content Sidebar ] start -->
                @if($is_many_languages)
                    <div class="content-sidebar content-sidebar-md" data-scrollbar-target="#psScrollbarInit">
                        <div class="content-sidebar-body">

                            <ul class="nav flex-column nxl-content-sidebar-item">
                                
                                @if($active_menu == "app_emails")

                                    @if(count($mail_inputs) > 0)
                                        
                                        @php $i=1; @endphp
                                        @foreach(getLanguageLocaleIds() as $loopLangCode => $loopLangId)
                                            <li class="nav-item flex-fill">
                                                <a href="javascript:void(0);" class="nav-link {{ ((count($tabs) < 2 && $i == 1) ? 'active' : '') }}" data-bs-target="#emails-tab-{{ $loopLangCode }}" data-bs-toggle="tab" aria-expanded="true">{!! langLabel('App Emails', $loopLangCode) !!}</a>
                                            </li>
                                            @php $i++; @endphp
                                        @endforeach    
                                    @endif

                                @endif
                                
                            </ul>
                        </div>
                    </div>
                @endif    
                <!-- [ Content Sidebar  ] end -->
                <!-- [ Main Area  ] start -->
                <div class="content-area" data-scrollbar-target="#psScrollbarInit">
                    <div class="content-area-body">
                        <div class="card mb-0">
                            <div class="card-body">
                                <div class="tab-content">
                                    
                                    <form method="post" class="has-validation-callback" id="app_text_form">
                                        
                                        <div class="tab-content" id="myTabContent">

                                            @if(count($mail_inputs) > 0)
                                                @php $i=1; @endphp
                                                @foreach(getLanguageLocaleIds() as $loopLangCode => $loopLangId)

                                                    <div class="tab-pane fade {{ ((count($tabs) < 2 && $i==1) ? 'show active' : '') }}" id="emails-tab-{{ $loopLangCode }}" role="tabpanel">
                                                        
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <div class="card border-top-0">
                                                                    <div class="card-header p-0">
                                                                        <!-- Nav tabs -->
                                                                        <ul class="nav nav-tabs flex-wrap w-100 text-center customers-nav-tabs" id="myTab" role="tablist">
                                                                            
                                                                            @foreach($mail_inputs as $mi => $mail_input)
                                                                                <li class="nav-item flex-fill border-top" role="presentation">
                                                                                    <a href="javascript:void(0);" class="nav-link {{ ($mi < 1 ? 'active' : '') }}" data-bs-toggle="tab" data-bs-target="#email-tab-{{ $loopLangCode }}-{{ $mi }}" role="tab">{{ $mail_input['heading']  }}</a>
                                                                                </li>
                                                                            @endforeach

                                                                        </ul>
                                                                    </div>
                                                                    <div class="tab-content">
                                                                        
                                                                        @php
                                                                            $default_from_address = config('mail.from.address');
                                                                            $default_from_name = config('mail.from.name');
                                                                        @endphp
                                                                        
                                                                        @foreach($mail_inputs as $mi => $mail_input)
                                                                        
                                                                            <div class="tab-pane fade {{ ($mi < 1 ? 'show active' : '') }} p-4" id="email-tab-{{ $loopLangCode }}-{{ $mi }}" role="tabpanel">
                                                                                
                                                                                @include("admin.form.email_management")

                                                                            </div>
                                                                        
                                                                        @endforeach

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                    @php $i++; @endphp
                                                @endforeach    

                                            @endif

                                        </div>

                                    </form>
                                </div>

                            </div>
                        </div>
                    </div>
                    
                </div>
                <!-- [ Content Area ] end -->
                
            </div>
            <!-- [ Main Content ] end -->

        @endif
            
    </div>

    @section('custom_javascript')
    <script src="{{ asset('public/assets') }}/js/tinymce/tinymce.bundle.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/select2.min.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/select2-active.min.js"></script>
        <script src="{{ asset('public/assets') }}/vendors/js/datepicker.min.js"></script>

        @if ($errors->any())
            @php
            $err_msgs = [];
            foreach ($errors->all() as $error) {
                $err_msgs[] = "- ".$error;
            }
            $form_error_msg = implode("<br>", $err_msgs);
            @endphp

            <script>
            jQuery(document).ready(function() {
                toast('{!! $form_error_msg !!}', 'danger');
            });
            </script>
        @endif

    @endsection

@endsection