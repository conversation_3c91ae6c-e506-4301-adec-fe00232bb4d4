<?php
namespace App\Code;

use Google\Client;

class GoogleClient  {

    public function client()  {
        
        $client = new Client();
        $client->setClientId(config('services.google.client_id'));
        $client->setClientSecret(config('services.google.client_secret'));
        $client->setRedirectUri(route('site_google_callback'));
        $client->setScopes(["email", "profile"]);
        
        $client->setHttpClient(new \GuzzleHttp\Client(['verify' => false]));
        return $client;
    }
}