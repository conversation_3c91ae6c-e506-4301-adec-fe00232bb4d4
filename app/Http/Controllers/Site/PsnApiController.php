<?php
namespace App\Http\Controllers\Site;

use App\Code\PsnApi;
use App\Code\PsnEncryption;
use App\Http\Controllers\Controller;
use App\Http\Models\PsnUser;
use App\Http\Models\StatsData;
use App\Http\Models\TrophiesDelta;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Psr\SimpleCache\InvalidArgumentException;
use Exception;

class PsnApiController extends Controller{
    /* @var PsnApi $psnApi */
    protected $psnApi = null;

	const UNIVERSAL_LIVE_LOGIN_GATE_URL = "https://ps.playstation.com/psn/login";

    /**
     * PsnApiController constructor.
     *
     * @param PsnApi $psnApi
     */
    public function __construct(PsnApi $psnApi){
        $this->psnApi = $psnApi;
    }

    private function getLoginGateUrl(){

        // Redirect user to psn login gate
        $defaultLoginUrl = static::UNIVERSAL_LIVE_LOGIN_GATE_URL;

        return $defaultLoginUrl;
    }

    /**
     * @return Application|RedirectResponse|Redirector|void
     */
    public function login(Request $request){

        //check if user is already logged in
        $psnUser = app(SiteController::class)->checkLoginSession();
        if($psnUser && $psnUser->user_uuid){
            return redirect(loginRedirectUrl());
        }

        //check if site is started for registration
        $site_live_time = strtotime(competitionStartDate()." 00:00:00");

        if(current_time() >= $site_live_time)  {

            $defaultLoginUrl = $this->getLoginGateUrl();
            $requestToken    = ["back_url" => route('token_auth')];

            return redirect($defaultLoginUrl . "?" . http_build_query(["t" => PsnEncryption::encryptText(json_encode($requestToken))]));
        }
        else  {
            return redirect(route('site_home'));
        }
    }


    /**
     * @param Request $request
     *
     * @return Application|RedirectResponse|Redirector|void
     */
    public function tokenAuth(Request $request)  {

        //check if site is started for registration
        $site_live_time = strtotime(competitionStartDate()." 00:00:00");
        if(current_time() < $site_live_time)  {
            return redirect(route('site_home'));
        }

        $redirectUrl = route('site_dashboard');

        // Try to login user
        if(isset($_GET["c"]) && $_GET["c"]){

            $accessToken = $this->getAccessTokenFromGatewayCode($_GET["c"]);
            $psnUserSess = $this->psnApi->handleTokenAuth($accessToken);
        }
        else if($request->query("code")){
            $accessToken = $this->psnApi->getAccessToken(sanitizeStrFull($request->query("code")));
            $psnUserSess = $this->psnApi->handleTokenAuth($accessToken);
        }
		else  {

            // Create shared php session
            startSharedSession();

            // Read service session
            $serviceSessionData = getServiceSession();

            // Sanity test
            if(!isset($serviceSessionData["token"]) && !isset($_GET["c"])){
                return redirect($redirectUrl);
            }

            $accessToken = $serviceSessionData["token"];
			$psnUserSess = $this->psnApi->handleTokenAuth($accessToken);
		}

        //if psn user set seesion and redirect
        if($psnUserSess) {

            $isSubAccount = $psnUserSess['isSubAccount'];

            if($isSubAccount)  {

                return redirect(route('site_sub_account_error'));
            }
            else  {
                
                $that = $this;
                $resp = advisoryLock(
                    $PsnUser['userUuid'] ?? (@request() ? @request()->ip() : "none"),
                    30,
                    static function () use ($psnUserSess, $that){
                        $that->savePsnUserData($psnUserSess);
                    }
                );

                return redirect(loginRedirectUrl());
            }
        }

        return redirect(route('site_home'));
    }


	private function getAccessTokenFromGatewayCode($code = "")  {

		$retrieveTokenUrl = str_replace("/login", "/retrieve_token", $this->getLoginGateUrl());
		$dataJson         = call_curl($retrieveTokenUrl . "?" . http_build_query(["access_code" => PsnEncryption::decryptText($code)]));
		$data             = json_decode($dataJson, true);

		return $data["data"]["token"] ?? "";
	}


    /**
     * @param $psnUserSess
     *
     * @throws InvalidArgumentException
     */
    public function savePsnUserData($psnUserSess){

        if(!isset($psnUserSess['isFakeUser']) || $psnUserSess['isFakeUser'] != "Y")  {
            if(@session()->has("debug_mode")) @session()->forget("debug_mode");
        }

        if(!test_mode())  {
            if(@session()->has("debug_mode_sec")) @session()->forget("debug_mode_sec");
        }

        $new_signup  = false;

        $psnUserSess['last_login_ip'] = random_string(20);
        $psnUserSess['expiresAt'] = date("Y-m-d H:i:s", time() + @intval($psnUserSess['expiresIn']));
        
        //$invoitePointsToUsers = false;
        $currentTime = date("Y-m-d H:i:s", current_time());

        if(isset($psnUserSess['userUuid'])){

            $loginPsnUser = app(SiteController::class)->checkLoginSession();

            //check if psn user is entered
            $psnUser = PsnUser::selectRaw("*". AESdecypts(PsnUser::encryptableFields()))->where("user_uuid", $psnUserSess['userUuid'])->first();

            if($loginPsnUser && $psnUser && $psnUser->id != $loginPsnUser->id)  {

                session()->flash('err_msg', fT('f_login.a_error.b_account_already_used', 'This account is already in use by other user!'));
                return redirect(route('site_dashboard'));
            }

            //check if psn user exists
            if($loginPsnUser)  {

                $psnUser = $loginPsnUser;
                $psnUser->online_id     = $psnUserSess['onlineId'];
                $psnUser->user_uuid     = $psnUserSess['userUuid'];
                $psnUser->country_code  = $psnUserSess['countryCode'];
                $psnUser->language_code = $psnUserSess['languageCode'];
            }
            else  {

                if(!$psnUser){

                    $new_signup  = true;
    
                    $last_login             = date('Y', time()).'-01-01 00:00:00';
                    $psnUser                = new PsnUser();
                    $psnUser->online_id     = $psnUserSess['onlineId'];
                    $psnUser->user_uuid     = $psnUserSess['userUuid'];
                    $psnUser->country_code  = $psnUserSess['countryCode'];
                    $psnUser->language_code = $psnUserSess['languageCode'];
                    $psnUser->logins        = 1;
                    $psnUser->first_login   = $currentTime;
                    $psnUser->created_at    = $currentTime;
                    $psnUser->signup_type   = "psn";
    
                    /*
                    //check if user has ref online id
                    $refPsnUser = null;
                    if(session()->get("ref_code")) {
    
                        $refPsnUser = PsnUser::where("ref_code", session()->get("ref_code"))->first();
    
                        if($refPsnUser) {
                            $psnUser->ref_user_id = $refPsnUser->id;
                            $invoitePointsToUsers = true;
                        }
                    }
                    */
                }
                else  {
                    $last_login      = $psnUser->last_login;
                    $psnUser->logins = $psnUser->logins + 1;
                }

                $psnUser->last_login     = $currentTime;
                $psnUser->last_login_ip  = $psnUserSess["last_login_ip"];

                if($psnUser->first_login == "")  {
                    $psnUser->first_login = $psnUser->last_login;
                }
            }
            
            $psnUser->access_token   = @json_encode($psnUserSess['accessToken']);
            $psnUser->refresh_token  = @json_encode($psnUserSess['refreshToken']);
            $psnUser->expires_in     = @json_encode($psnUserSess['expiresIn']);
            $psnUser->ps_plus_status = $psnUserSess['psPlusStatus'];
            $psnUser->user_id        = $psnUserSess['userId'];
            $psnUser->dcim_id        = $psnUserSess['dcimId'];
            $psnUser->expires_at     = $psnUserSess['expiresAt'];

            if($psnUser->nickname == "")  {

                $final_nickname = $this->nicknameExists($psnUser->online_id);

                $psnUser->nickname = $final_nickname;
                $psnUser->tmp_nickname = 1;
            }
            
            //check if avtar image is added
            if($psnUser->avtar_url == "")  {

                $avtar_image_url = "";

                //get avtar url
                try{

                    // Fetch user profile
                    $userProfile = $this->psnApi->getUserDetail($psnUser);

                    if(isset($userProfile['profile']['avatarUrls'][0]['avatarUrl']))
                        $avtar_image_url = $userProfile['profile']['avatarUrls'][0]['avatarUrl'];
                }
                catch(Exception $e){  }
                $psnUser->avtar_url = $avtar_image_url;
            }

            //save psn user
            $psnUser->save();

            
            //give ps plus membership ticket
            if($psnUser->ps_plus_status == "member" && !$psnUser->ticket_exists("psplus-membership")){

                //give ticket to user for ps plus membership
                $label      = array();
                $tickets     = fS('tickets.psplus-membership', 1);
                $identifier = "psplus-membership";
                $transactionId    = fS('transaction.psplus_mebership');
                $points_added   = $psnUser->add_tickets($label, $tickets, $identifier, $transactionId, 'membership');
            }

            /*
            //give invite points to new user
            if($invoitePointsToUsers)  {

                //give points to referral user - This will go only once as it will check for duplicate identifier
                $pointsKeyAdd = strtolower($refPsnUser->country_code) == "pl" ? "-pl" : "";

                $ref_label        = array("[LABEL]" => $psnUser->online_id);
                $ref_points       = fS('points.invite-friends'.$pointsKeyAdd, 500);
                $ref_identifier   = "invite-friend-" . $psnUser->id;
                $transactionId    = fS('transaction.invite_friends');
                $ref_points_added = $refPsnUser->add_tickets($ref_label, $ref_points, $ref_identifier, $transactionId, 'invite');

                if($ref_points_added !== false){

                    //update stats
                    StatsData::incrementStat('invite-success');

                    //Send push notification about points
                    $push_data = getPushData($refPsnUser, $transactionId, $ref_label, $ref_points, $ref_identifier);
                    if($push_data)  {
                        $this->sendPointsPushNotification($refPsnUser, $push_data);
                    }
                }

                //give points to new user
                $pointsKeyAdd = strtolower($psnUser->country_code) == "pl" ? "-pl" : "";

                $ref_label      = array("[LABEL]" => $refPsnUser->online_id);
                $ref_points     = fS('points.invite-friends'.$pointsKeyAdd, 500);
                $ref_identifier = "accept-invitation-" . $refPsnUser->id;
                $points_added   = $psnUser->add_tickets($ref_label, $ref_points, $ref_identifier, 'invite');
            }
            */

            if(!$loginPsnUser)  {

                /*
                if($psnUser->ref_code == "")  {
                    $psnUser->ref_code = $this->generateUniqueRefCodeFast($psnUser);
                    $psnUser->save();
                }
                */
                
                $psnUserSess["id"] = @$psnUser->id;
                session()->put("psn_user", $psnUserSess);

                if($new_signup)  {

                    //update stats
                    StatsData::incrementStat('unique-users');
                    StatsData::incrementStat('unique-login');
                }
                else {
    
                    if(date('Y-m-d', strtotime($last_login)) != date('Y-m-d', strtotime($psnUser->last_login)))  {
    
                        //update stats
                        StatsData::incrementStat('unique-login');
    
                        session()->put("first_time_login_today", 1);
                    }
                }
    
                StatsData::incrementStat('logins');
            }
            
            //get trophies of user
            try{
                $that = $this;
                advisoryLock(
                    ("userTrophiesChanged_" . (@$psnUser->id ?? (@request() ? @request()->ip() : "none")))
                    ?? (@request() ? @request()->ip() : "none"),
                    30,
                    static function () use ($psnUser, $that){
                        $resp = $that->userTrophiesChanged($psnUser);
                    });
            }
            catch(Exception $e){
                Log::error(sprintf("%s - %s", shortClassMethod(__METHOD__), @var_export($e->getMessage(), true)));
            }
        }
    }


    /**
     * @param PsnUser $psnUser
     *
     * @return TrophiesDelta|null
     * @throws InvalidArgumentException
     */
    public function userTrophiesChanged(PsnUser $psnUser){

        // Prepare vars
        $bronzeNum            = 0; // total earned
        $silverNum            = 0; // total earned
        $goldNum              = 0; // total earned
        $platinumNum          = 0; // total earned

        // Fetch user profile
        $user_detail = $this->psnApi->getUserDetail($psnUser);

        if(isset($user_detail['profile']['trophySummary']['earnedTrophies']))  {

            // Count trophies earned
            $earnedTrophies = $user_detail['profile']['trophySummary']['earnedTrophies'];

            $bronzeNum    = isset($earnedTrophies['bronze']) ? $earnedTrophies['bronze'] : 0;
            $silverNum    = isset($earnedTrophies['silver']) ? $earnedTrophies['silver'] : 0;
            $goldNum      = isset($earnedTrophies['gold']) ? $earnedTrophies['gold'] : 0;
            $platinumNum  = isset($earnedTrophies['platinum']) ? $earnedTrophies['platinum'] : 0;
        }

        // Calculate earned delta this check
        $deltaBronzeNum   = $bronzeNum - $psnUser->bronze;
        $deltaSilverNum   = $silverNum - $psnUser->silver;
        $deltaGoldNum     = $goldNum - $psnUser->gold;
        $deltaPlatinumNum = $platinumNum - $psnUser->platinum;

        // Add points
        $day = get_calendar_date();
        $dayTime = strtotime($day." 00:00:00");

        if($dayTime >= strtotime(competitionStartDate().' 00:00:00') && $dayTime <= strtotime(competitionEndDate().' 23:59:59'))  {

            $firstLogin         = $psnUser->last_psn_login ? false : true;
            $psn_last_login     = $psnUser->last_psn_login ?? "2025-01-01";

            //save last psn login time
            $psnUser->last_psn_login = date('Y-m-d H:i:s', current_time());
            $psnUser->save();
            
            $trophiesDelta = TrophiesDelta::where('user_id', $psnUser->id)->where('day', $day)->first();

            if(($deltaBronzeNum || $deltaSilverNum || $deltaGoldNum || $deltaPlatinumNum) || $firstLogin)  {
    
                // Log changes
                if(!$trophiesDelta)  {

                    $trophiesDelta             = new TrophiesDelta;
                    $trophiesDelta->user_id    = $psnUser->id;
                    $trophiesDelta->day        = ($firstLogin ? NULL : $day);

                    $trophiesDelta->max_tickets = 0;
                    $trophiesDelta->tickets    = 0;
                    $trophiesDelta->bronze     = 0;
                    $trophiesDelta->silver     = 0;
                    $trophiesDelta->gold       = 0;
                    $trophiesDelta->platinum   = 0;
                }

                $trophiesDelta->trophy_level = $user_detail['profile']['trophySummary']['level'];
                $trophiesDelta->bronze     += $deltaBronzeNum;
                $trophiesDelta->silver     += $deltaSilverNum;
                $trophiesDelta->gold       += $deltaGoldNum;
                $trophiesDelta->platinum   += $deltaPlatinumNum;

                $ticketsEarned = 0;
                if($firstLogin)  {

                    $label         = [];
                    $ticketsEarned = floor($trophiesDelta->trophy_level / 100) + 1;
                    
                    if($ticketsEarned > 10)
                        $ticketsEarned = 10;

                    if($bronzeNum < 1 && $silverNum < 1 && $goldNum < 1 && $platinumNum < 1)
                        $ticketsEarned = 0;
                }
                else  {

                    if($trophiesDelta->tickets < 2 || $trophiesDelta->tickets < $trophiesDelta->max_tickets)  {

                        //total new trophy got after last login
                        $newTicketsEarned = $deltaBronzeNum + $deltaSilverNum + $deltaGoldNum + $deltaPlatinumNum;

                        if($trophiesDelta->max_tickets < 1)  {

                            //get day difference between 2 logins
                            $lastLoginDate = date('Y-m-d', strtotime($psn_last_login));
                            $time_diff = strtotime($psnUser->last_psn_login) - strtotime($lastLoginDate." 00:00:00");
                            $day_diff = ceil($time_diff / 86400);
                            $day_diff = $day_diff < 1 ? 1 : $day_diff;

                            //max tickets for this login
                            $maxTicketsPerDay = 2;
                            $maxTicketsTotal = $maxTicketsPerDay * $day_diff;

                            //check tickets of last login day
                            $oldTrophiesDelta = TrophiesDelta::where('user_id', $psnUser->id)->where('day', $lastLoginDate)->first();
                            if($oldTrophiesDelta)  {

                                $removeTickets = $oldTrophiesDelta->tickets > 2 ? 2 : $oldTrophiesDelta->tickets;
                                $maxTicketsTotal = $maxTicketsTotal - $removeTickets;
                            }

                            $trophiesDelta->max_tickets = $maxTicketsTotal;
                        }
                        else  {

                            $maxTicketsTotal = $trophiesDelta->max_tickets;
                        }

                        if($newTicketsEarned > $maxTicketsTotal)
                            $newTicketsEarned = $maxTicketsTotal;

                        $ticketsEarned = $newTicketsEarned - $trophiesDelta->tickets;
                    }
                }

                $trophiesDelta->tickets += $ticketsEarned;
                
                $trophiesDelta->save();

                if($firstLogin)
                    session()->put("last_trophy_delta_id", $trophiesDelta->id);
    
                // Add points - no need to add points for friend invites as it is already given
                if($ticketsEarned > 0) {
    
                    $identifier = $firstLogin ? 'initial-trophies' : 'trophies-delta-' . $trophiesDelta->id.'-'.date('his', current_time());

                    $label         = ["[TICKETS]" => $ticketsEarned];
                    $transactionId = fS('transaction.earn_trophies');
                    $new_tickets = $psnUser->add_tickets($label, $ticketsEarned, $identifier, $transactionId, "trophy-game");
                }
                
                // Update psn user
                $psnUser->trophy_level = $user_detail['profile']['trophySummary']['level'];
                $psnUser->bronze   = $bronzeNum;
                $psnUser->silver   = $silverNum;
                $psnUser->gold     = $goldNum;
                $psnUser->platinum = $platinumNum;
                $psnUser->save();
            }

            //get points after last login
            $last_credits_tickets = [];
            
            //find ps plus membership after last login
            $psPlusNum       = $psnUser->ticketCounts('psplus-membership', $psn_last_login);
            if($psPlusNum > 0)
                $last_credits_tickets['psplus-membership'] = $psPlusNum;

            /*
            //find new friend invite points after last login
            $friendInviteNum    = $psnUser->ticketCounts('invite-friend-', $psn_last_login);
            if($friendInviteNum > 0)
                $last_credits_tickets['invite-friend'] = $friendInviteNum;
            
            $goldenTicketNum = $psnUser->ticketCounts('golden-ticket', $psn_last_login);
            if($goldenTicketNum > 0)
                $last_credits_tickets['golden-ticket'] = $goldenTicketNum;
            */

            if($firstLogin)
                session()->put("last_credits_tickets", $last_credits_tickets);

            //Ret trophies delta
            return $trophiesDelta;
        }

        return false;
    }


    /**
     * @param $psnUser
     *
     * @return String $ref_code
     */
    public function generateUniqueRefCodeFast($psnUser)  {
        
        return Str::limit(Str::random(mt_rand(0, 4)) . $psnUser->id . Str::random(mt_rand(0, 4)) . time() . Str::random(10), 20, ""); // total length = 20
    }
}